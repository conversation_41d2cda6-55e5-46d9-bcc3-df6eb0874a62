import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'GUCCI® US Official Site | Redefining Luxury Fashion',
  description: 'Discover the latest collections from Gucci. Shop luxury handbags, shoes, ready-to-wear, jewelry, watches, and beauty.',
  keywords: 'Gucci, luxury fashion, handbags, shoes, jewelry, watches, beauty',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@100;300;400;500;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`${inter.className} font-gucci antialiased`}>
        {children}
      </body>
    </html>
  )
}
