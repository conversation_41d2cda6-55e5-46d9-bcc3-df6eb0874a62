'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Heart, ShoppingBag, Menu, X } from 'lucide-react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navItems = [
    'Women', 'Men', 'Handbags', 'Shoes', 'Jewelry & Watches', 
    'Beauty', 'Gifts', 'Gucci Services', 'World of Gucci'
  ]

  return (
    <>
      <motion.header
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          isScrolled
            ? 'bg-gucci-white/95 backdrop-blur-md shadow-sm'
            : 'bg-black/20 backdrop-blur-sm'
        }`}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8, ease: [0.5, 0, 0, 1] }}
      >
        <div className="container-gucci">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Mobile Menu Button */}
            <button
              className={`lg:hidden p-2 transition-colors duration-300 ${
                isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
              }`}
              onClick={() => setIsMenuOpen(true)}
              aria-label="Open menu"
            >
              <Menu size={24} />
            </button>

            {/* Logo */}
            <motion.div
              className="flex-1 lg:flex-none text-center lg:text-left"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <h1 className={`text-2xl lg:text-3xl font-light tracking-widest transition-colors duration-500 ${
                isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-lg'
              }`}>
                GUCCI
              </h1>
            </motion.div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex flex-1 justify-center">
              <ul className="flex space-x-8">
                {navItems.map((item, index) => (
                  <motion.li
                    key={item}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                  >
                    <a
                      href="#"
                      className={`text-sm font-medium tracking-wide hover:opacity-70 transition-all duration-300 ${
                        isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
                      }`}
                    >
                      {item}
                    </a>
                  </motion.li>
                ))}
              </ul>
            </nav>

            {/* Action Buttons */}
            <div className="flex items-center space-x-4">
              <motion.button
                className={`p-2 hover:opacity-70 transition-all duration-300 ${
                  isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsSearchOpen(true)}
                aria-label="Search"
              >
                <Search size={20} />
              </motion.button>
              <motion.button
                className={`p-2 hover:opacity-70 transition-all duration-300 ${
                  isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                aria-label="Wishlist"
              >
                <Heart size={20} />
              </motion.button>
              <motion.button
                className={`p-2 hover:opacity-70 transition-all duration-300 ${
                  isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                aria-label="Shopping bag"
              >
                <ShoppingBag size={20} />
              </motion.button>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            className="fixed inset-0 z-50 lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="absolute inset-0 bg-black/50" onClick={() => setIsMenuOpen(false)} />
            <motion.div
              className="absolute left-0 top-0 h-full w-80 bg-gucci-white"
              initial={{ x: -320 }}
              animate={{ x: 0 }}
              exit={{ x: -320 }}
              transition={{ duration: 0.4, ease: [0.5, 0, 0, 1] }}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-2xl font-light tracking-widest">GUCCI</h2>
                  <button
                    onClick={() => setIsMenuOpen(false)}
                    className="p-2"
                    aria-label="Close menu"
                  >
                    <X size={24} />
                  </button>
                </div>
                <nav>
                  <ul className="space-y-6">
                    {navItems.map((item, index) => (
                      <motion.li
                        key={item}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1, duration: 0.4 }}
                      >
                        <a
                          href="#"
                          className="block text-lg font-medium tracking-wide hover:opacity-70 transition-opacity duration-300"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {item}
                        </a>
                      </motion.li>
                    ))}
                  </ul>
                </nav>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Search Modal */}
      <AnimatePresence>
        {isSearchOpen && (
          <motion.div
            className="fixed inset-0 z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div
              className="absolute inset-0 bg-black/80 backdrop-blur-md"
              onClick={() => setIsSearchOpen(false)}
            />
            <motion.div
              className="absolute inset-0 flex items-center justify-center p-4"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ duration: 0.4, ease: [0.5, 0, 0, 1] }}
            >
              <div className="w-full max-w-2xl bg-white rounded-lg shadow-2xl overflow-hidden">
                {/* Search Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-100">
                  <h3 className="text-xl font-light tracking-wide">Search Gucci</h3>
                  <button
                    onClick={() => setIsSearchOpen(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    aria-label="Close search"
                  >
                    <X size={20} />
                  </button>
                </div>

                {/* Search Input */}
                <div className="p-6">
                  <div className="relative">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                      type="text"
                      placeholder="Search for products, collections, or stories..."
                      className="w-full pl-12 pr-4 py-4 text-lg border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-gucci-black focus:border-transparent transition-all duration-300"
                      autoFocus
                    />
                  </div>
                </div>

                {/* Popular Searches */}
                <div className="px-6 pb-6">
                  <h4 className="text-sm font-medium text-gray-600 uppercase tracking-wide mb-4">
                    Popular Searches
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {[
                      'Handbags',
                      'Sneakers',
                      'GG Marmont',
                      'Dionysus',
                      'Ace Sneakers',
                      'Jewelry',
                      'Watches',
                      'Beauty'
                    ].map((term, index) => (
                      <motion.button
                        key={term}
                        className="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gucci-black hover:text-white transition-all duration-300"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05, duration: 0.3 }}
                      >
                        {term}
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* Quick Links */}
                <div className="bg-gray-50 px-6 py-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <a href="#" className="flex items-center space-x-2 text-gray-600 hover:text-gucci-black transition-colors duration-200">
                      <span>New Arrivals</span>
                    </a>
                    <a href="#" className="flex items-center space-x-2 text-gray-600 hover:text-gucci-black transition-colors duration-200">
                      <span>Sale</span>
                    </a>
                    <a href="#" className="flex items-center space-x-2 text-gray-600 hover:text-gucci-black transition-colors duration-200">
                      <span>Gift Guide</span>
                    </a>
                    <a href="#" className="flex items-center space-x-2 text-gray-600 hover:text-gucci-black transition-colors duration-200">
                      <span>Store Locator</span>
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

export default Header
