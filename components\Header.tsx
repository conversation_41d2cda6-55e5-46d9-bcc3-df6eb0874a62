'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Heart, ShoppingBag, Menu, X } from 'lucide-react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navItems = [
    'Women', 'Men', 'Handbags', 'Shoes', 'Jewelry & Watches', 
    'Beauty', 'Gifts', 'Gucci Services', 'World of Gucci'
  ]

  return (
    <>
      <motion.header
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          isScrolled
            ? 'bg-gucci-white/95 backdrop-blur-md shadow-sm'
            : 'bg-black/20 backdrop-blur-sm'
        }`}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8, ease: [0.5, 0, 0, 1] }}
      >
        <div className="container-gucci">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Mobile Menu Button */}
            <button
              className={`lg:hidden p-2 transition-colors duration-300 ${
                isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
              }`}
              onClick={() => setIsMenuOpen(true)}
              aria-label="Open menu"
            >
              <Menu size={24} />
            </button>

            {/* Logo */}
            <motion.div
              className="flex-1 lg:flex-none text-center lg:text-left"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <h1 className={`text-2xl lg:text-3xl font-light tracking-widest transition-colors duration-500 ${
                isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-lg'
              }`}>
                GUCCI
              </h1>
            </motion.div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex flex-1 justify-center">
              <ul className="flex space-x-8">
                {navItems.map((item, index) => (
                  <motion.li
                    key={item}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                  >
                    <a
                      href="#"
                      className={`text-sm font-medium tracking-wide hover:opacity-70 transition-all duration-300 ${
                        isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
                      }`}
                    >
                      {item}
                    </a>
                  </motion.li>
                ))}
              </ul>
            </nav>

            {/* Action Buttons */}
            <div className="flex items-center space-x-4">
              <motion.button
                className={`p-2 hover:opacity-70 transition-all duration-300 ${
                  isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsSearchOpen(true)}
                aria-label="Search"
              >
                <Search size={20} />
              </motion.button>
              <motion.button
                className={`p-2 hover:opacity-70 transition-all duration-300 ${
                  isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                aria-label="Wishlist"
              >
                <Heart size={20} />
              </motion.button>
              <motion.button
                className={`p-2 hover:opacity-70 transition-all duration-300 ${
                  isScrolled ? 'text-gucci-black' : 'text-white drop-shadow-md'
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                aria-label="Shopping bag"
              >
                <ShoppingBag size={20} />
              </motion.button>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            className="fixed inset-0 z-50 lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="absolute inset-0 bg-black/50" onClick={() => setIsMenuOpen(false)} />
            <motion.div
              className="absolute left-0 top-0 h-full w-80 bg-gucci-white"
              initial={{ x: -320 }}
              animate={{ x: 0 }}
              exit={{ x: -320 }}
              transition={{ duration: 0.4, ease: [0.5, 0, 0, 1] }}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-2xl font-light tracking-widest">GUCCI</h2>
                  <button
                    onClick={() => setIsMenuOpen(false)}
                    className="p-2"
                    aria-label="Close menu"
                  >
                    <X size={24} />
                  </button>
                </div>
                <nav>
                  <ul className="space-y-6">
                    {navItems.map((item, index) => (
                      <motion.li
                        key={item}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1, duration: 0.4 }}
                      >
                        <a
                          href="#"
                          className="block text-lg font-medium tracking-wide hover:opacity-70 transition-opacity duration-300"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {item}
                        </a>
                      </motion.li>
                    ))}
                  </ul>
                </nav>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Search Modal */}
      <AnimatePresence>
        {isSearchOpen && (
          <motion.div
            className="fixed inset-0 z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5, ease: [0.5, 0, 0, 1] }}
          >
            <div
              className="absolute inset-0 bg-gucci-black/90 backdrop-blur-sm"
              onClick={() => setIsSearchOpen(false)}
            />
            <motion.div
              className="absolute inset-0 flex items-start justify-center pt-32 p-4"
              initial={{ y: -50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -50, opacity: 0 }}
              transition={{ duration: 0.6, ease: [0.5, 0, 0, 1] }}
            >
              <div className="w-full max-w-4xl">
                {/* Close Button */}
                <motion.button
                  onClick={() => setIsSearchOpen(false)}
                  className="absolute top-8 right-8 p-3 text-white hover:opacity-70 transition-opacity duration-300"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  aria-label="Close search"
                >
                  <X size={24} />
                </motion.button>

                {/* Search Input */}
                <div className="mb-12">
                  <motion.input
                    type="text"
                    placeholder="Search"
                    className="w-full bg-transparent border-0 border-b-2 border-white/30 text-white text-4xl lg:text-6xl font-light tracking-wide placeholder-white/50 focus:outline-none focus:border-white pb-4 transition-all duration-500"
                    autoFocus
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.6 }}
                  />
                </div>

                {/* Popular Searches */}
                <motion.div
                  className="mb-16"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.6 }}
                >
                  <h4 className="text-white/60 text-sm font-medium uppercase tracking-widest mb-8">
                    Popular Searches
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    {[
                      'Handbags',
                      'Sneakers',
                      'GG Marmont',
                      'Dionysus',
                      'Ace Sneakers',
                      'Jewelry',
                      'Watches',
                      'Beauty'
                    ].map((term, index) => (
                      <motion.button
                        key={term}
                        className="text-left text-white text-lg font-light tracking-wide hover:opacity-70 transition-opacity duration-300 border-b border-white/20 pb-2"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 + index * 0.1, duration: 0.4 }}
                        whileHover={{ x: 10 }}
                      >
                        {term}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>

                {/* Categories */}
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.6 }}
                >
                  <h4 className="text-white/60 text-sm font-medium uppercase tracking-widest mb-8">
                    Categories
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {[
                      { title: 'Women', items: ['Ready-to-Wear', 'Handbags', 'Shoes', 'Jewelry'] },
                      { title: 'Men', items: ['Ready-to-Wear', 'Bags', 'Shoes', 'Accessories'] },
                      { title: 'Gifts', items: ['For Her', 'For Him', 'Beauty', 'Home'] }
                    ].map((category, categoryIndex) => (
                      <motion.div
                        key={category.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.7 + categoryIndex * 0.1, duration: 0.4 }}
                      >
                        <h5 className="text-white text-xl font-light tracking-wide mb-4">
                          {category.title}
                        </h5>
                        <ul className="space-y-2">
                          {category.items.map((item, itemIndex) => (
                            <motion.li
                              key={item}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: 0.8 + categoryIndex * 0.1 + itemIndex * 0.05, duration: 0.3 }}
                            >
                              <a
                                href="#"
                                className="text-white/70 hover:text-white transition-colors duration-300 text-sm tracking-wide"
                              >
                                {item}
                              </a>
                            </motion.li>
                          ))}
                        </ul>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

export default Header
