/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Capp%5Cpage.tsx&server=true!":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Capp%5Cpage.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYnJhZHklNUNEZXNrdG9wJTVDcG9ydCU1Q2FwcCU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2d1Y2NpLXdlYnNpdGUtY2xvbmUvPzEyY2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxicmFkeVxcXFxEZXNrdG9wXFxcXHBvcnRcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Capp%5Cglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Capp%5Cglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Hero */ \"(ssr)/./components/Hero.tsx\");\n/* harmony import */ var _components_FeaturedCollections__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/FeaturedCollections */ \"(ssr)/./components/FeaturedCollections.tsx\");\n/* harmony import */ var _components_NewsletterSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NewsletterSection */ \"(ssr)/./components/NewsletterSection.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./components/Footer.tsx\");\n/* harmony import */ var _components_ProgressIndicator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ProgressIndicator */ \"(ssr)/./components/ProgressIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Home() {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading time\n        const timer = setTimeout(()=>{\n            setIsLoading(false);\n        }, 1000);\n        return ()=>clearTimeout(timer);\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gucci-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-4xl font-light tracking-widest mb-4\",\n                        children: \"GUCCI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"loading-shimmer h-1 w-32 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gucci-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressIndicator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FeaturedCollections__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewsletterSection__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\page.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/FeaturedCollections.tsx":
/*!********************************************!*\
  !*** ./components/FeaturedCollections.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst FeaturedCollections = ()=>{\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView)(ref, {\n        once: true,\n        amount: 0.2\n    });\n    const collections = [\n        {\n            id: 1,\n            title: \"Signature Handbags\",\n            subtitle: \"Iconic designs reimagined\",\n            image: \"https://images.unsplash.com/photo-1584917865442-de89df76afd3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1935&q=80\",\n            link: \"#\"\n        },\n        {\n            id: 2,\n            title: \"Luxury Footwear\",\n            subtitle: \"Step into elegance\",\n            image: \"https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2012&q=80\",\n            link: \"#\"\n        },\n        {\n            id: 3,\n            title: \"Fine Jewelry\",\n            subtitle: \"Timeless luxury pieces\",\n            image: \"https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n            link: \"#\"\n        },\n        {\n            id: 4,\n            title: \"Ready-to-Wear\",\n            subtitle: \"Contemporary luxury\",\n            image: \"https://images.unsplash.com/photo-1434389677669-e08b4cac3105?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2005&q=80\",\n            link: \"#\"\n        },\n        {\n            id: 5,\n            title: \"Beauty Collection\",\n            subtitle: \"Discover Gucci Beauty\",\n            image: \"https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80\",\n            link: \"#\"\n        },\n        {\n            id: 6,\n            title: \"Watches\",\n            subtitle: \"Precision meets luxury\",\n            image: \"https://images.unsplash.com/photo-1524592094714-0f0654e20314?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1999&q=80\",\n            link: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"py-20 lg:py-32 bg-gucci-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-gucci\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 50\n                    },\n                    animate: {\n                        opacity: isInView ? 1 : 0,\n                        y: isInView ? 0 : 50\n                    },\n                    transition: {\n                        duration: 0.8,\n                        ease: [\n                            0.5,\n                            0,\n                            0,\n                            1\n                        ]\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-5xl font-light tracking-wide mb-4\",\n                            children: \"Featured Collections\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Explore our curated selection of luxury pieces that define contemporary elegance\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: collections.map((collection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"group cursor-pointer\",\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            animate: {\n                                opacity: isInView ? 1 : 0,\n                                y: isInView ? 0 : 50\n                            },\n                            transition: {\n                                delay: index * 0.1,\n                                duration: 0.8,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            whileHover: {\n                                y: -10\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden bg-gray-100 aspect-[4/5] mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: collection.image,\n                                            alt: collection.title,\n                                            fill: true,\n                                            className: \"object-cover transition-transform duration-700 group-hover:scale-110\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-end p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-medium mb-2\",\n                                                        children: collection.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm opacity-90 mb-4\",\n                                                        children: collection.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                        className: \"border border-white text-white px-6 py-2 text-sm font-medium tracking-wide uppercase hover:bg-white hover:text-black transition-all duration-300\",\n                                                        whileHover: {\n                                                            scale: 1.05\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        children: \"Shop Now\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2 group-hover:opacity-70 transition-opacity duration-300\",\n                                            children: collection.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: collection.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, collection.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\FeaturedCollections.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeaturedCollections);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/FeaturedCollections.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Footer = ()=>{\n    const footerSections = [\n        {\n            title: \"May We Help You?\",\n            links: [\n                \"Contact Us\",\n                \"My Order\",\n                \"FAQs\",\n                \"Email Unsubscribe\",\n                \"Sitemap\"\n            ]\n        },\n        {\n            title: \"The Company\",\n            links: [\n                \"About Gucci\",\n                \"Gucci Equilibrium\",\n                \"Code of Ethics\",\n                \"Careers\",\n                \"Legal\",\n                \"Privacy & Cookie Policy\"\n            ]\n        },\n        {\n            title: \"Gucci Services\",\n            links: [\n                \"Discover Our Services\",\n                \"Book an Appointment\",\n                \"Collect In Store\",\n                \"Personalization\",\n                \"Gift Wrapping\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gucci-black text-gucci-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-gucci py-16 lg:py-24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            className: \"lg:col-span-1\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-light tracking-widest mb-6\",\n                                    children: \"GUCCI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm opacity-80 leading-relaxed mb-6\",\n                                    children: \"Redefining luxury fashion with Italian craftsmanship and contemporary design. Discover our heritage of excellence and innovation.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        \"IG\",\n                                        \"FB\",\n                                        \"TW\",\n                                        \"YT\"\n                                    ].map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a, {\n                                            href: \"#\",\n                                            className: \"w-10 h-10 border border-white/30 flex items-center justify-center text-xs font-medium hover:bg-white hover:text-black transition-all duration-300\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                delay: index * 0.1,\n                                                duration: 0.6\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: social\n                                        }, social, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        footerSections.map((section, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: (sectionIndex + 1) * 0.2,\n                                    duration: 0.8,\n                                    ease: [\n                                        0.5,\n                                        0,\n                                        0,\n                                        1\n                                    ]\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium tracking-wide uppercase mb-6 opacity-60\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: section.links.map((link, linkIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.li, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: (sectionIndex + 1) * 0.2 + linkIndex * 0.1,\n                                                    duration: 0.6\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-sm hover:opacity-70 transition-opacity duration-300 block\",\n                                                    children: link\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, link, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, section.title, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, undefined))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    className: \"border-t border-white/20 mt-16 pt-8 flex flex-col lg:flex-row justify-between items-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.8,\n                        duration: 0.8,\n                        ease: [\n                            0.5,\n                            0,\n                            0,\n                            1\n                        ]\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-60 mb-4 lg:mb-0\",\n                            children: \"\\xa9 2016 - 2025 Guccio Gucci S.p.A. - All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-6 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"hover:opacity-70 transition-opacity duration-300\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"hover:opacity-70 transition-opacity duration-300\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"hover:opacity-70 transition-opacity duration-300\",\n                                    children: \"Cookie Settings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"hover:opacity-70 transition-opacity duration-300\",\n                                    children: \"Accessibility\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Footer.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 50);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navItems = [\n        \"Women\",\n        \"Men\",\n        \"Handbags\",\n        \"Shoes\",\n        \"Jewelry & Watches\",\n        \"Beauty\",\n        \"Gifts\",\n        \"Gucci Services\",\n        \"World of Gucci\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.header, {\n                className: `fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${isScrolled ? \"bg-gucci-white/95 backdrop-blur-md shadow-sm\" : \"bg-transparent\"}`,\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8,\n                    ease: [\n                        0.5,\n                        0,\n                        0,\n                        1\n                    ]\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-gucci\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 lg:h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"lg:hidden p-2\",\n                                onClick: ()=>setIsMenuOpen(true),\n                                \"aria-label\": \"Open menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"flex-1 lg:flex-none text-center lg:text-left\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl lg:text-3xl font-light tracking-widest\",\n                                    children: \"GUCCI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden lg:flex flex-1 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"flex space-x-8\",\n                                    children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.li, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: index * 0.1,\n                                                duration: 0.6\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm font-medium tracking-wide hover:opacity-70 transition-opacity duration-300\",\n                                                children: item\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, item, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"p-2 hover:opacity-70 transition-opacity duration-300\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        \"aria-label\": \"Search\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"p-2 hover:opacity-70 transition-opacity duration-300\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        \"aria-label\": \"Wishlist\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"p-2 hover:opacity-70 transition-opacity duration-300\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        \"aria-label\": \"Shopping bag\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"fixed inset-0 z-50 lg:hidden\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/50\",\n                            onClick: ()=>setIsMenuOpen(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute left-0 top-0 h-full w-80 bg-gucci-white\",\n                            initial: {\n                                x: -320\n                            },\n                            animate: {\n                                x: 0\n                            },\n                            exit: {\n                                x: -320\n                            },\n                            transition: {\n                                duration: 0.4,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-light tracking-widest\",\n                                                children: \"GUCCI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsMenuOpen(false),\n                                                className: \"p-2\",\n                                                \"aria-label\": \"Close menu\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-6\",\n                                            children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.li, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1,\n                                                        duration: 0.4\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"block text-lg font-medium tracking-wide hover:opacity-70 transition-opacity duration-300\",\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        children: item\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, item, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Hero.tsx":
/*!*****************************!*\
  !*** ./components/Hero.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Hero = ()=>{\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView)(ref, {\n        once: true,\n        amount: 0.3\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"relative h-screen overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"absolute inset-0\",\n                initial: {\n                    scale: 1.1\n                },\n                animate: {\n                    scale: isInView ? 1 : 1.1\n                },\n                transition: {\n                    duration: 1.5,\n                    ease: [\n                        0.5,\n                        0,\n                        0,\n                        1\n                    ]\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        src: \"https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n                        alt: \"Gucci Fall Winter 2025 Collection\",\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 h-full flex items-center justify-center text-center text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            animate: {\n                                opacity: isInView ? 1 : 0,\n                                y: isInView ? 0 : 50\n                            },\n                            transition: {\n                                delay: 0.5,\n                                duration: 0.8,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block px-4 py-2 bg-white/10 backdrop-blur-sm rounded text-sm font-medium tracking-wide uppercase mb-4\",\n                                children: \"New Collection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                            className: \"text-4xl md:text-6xl lg:text-7xl font-light tracking-wide mb-6\",\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            animate: {\n                                opacity: isInView ? 1 : 0,\n                                y: isInView ? 0 : 50\n                            },\n                            transition: {\n                                delay: 0.7,\n                                duration: 0.8,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            children: \"Fall Winter 2025\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                            className: \"text-xl md:text-2xl font-light mb-8 opacity-90\",\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            animate: {\n                                opacity: isInView ? 0.9 : 0,\n                                y: isInView ? 0 : 50\n                            },\n                            transition: {\n                                delay: 0.9,\n                                duration: 0.8,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            children: \"Discover the new collection\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            initial: {\n                                opacity: 0,\n                                y: 50\n                            },\n                            animate: {\n                                opacity: isInView ? 1 : 0,\n                                y: isInView ? 0 : 50\n                            },\n                            transition: {\n                                delay: 1.1,\n                                duration: 0.8,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    className: \"bg-white text-black px-8 py-4 text-sm font-medium tracking-wide uppercase transition-all duration-300 hover:bg-gray-100\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: \"Explore Women's\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                    className: \"border border-white text-white px-8 py-4 text-sm font-medium tracking-wide uppercase transition-all duration-300 hover:bg-white hover:text-black\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: \"Explore Men's\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 1.5,\n                    duration: 0.8\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"w-6 h-10 border-2 border-white rounded-full flex justify-center\",\n                    animate: {\n                        y: [\n                            0,\n                            10,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"w-1 h-3 bg-white rounded-full mt-2\",\n                        animate: {\n                            opacity: [\n                                1,\n                                0,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Hero.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Hero);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./components/NewsletterSection.tsx":
/*!******************************************!*\
  !*** ./components/NewsletterSection.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst NewsletterSection = ()=>{\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        amount: 0.3\n    });\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (email) {\n            setIsSubmitted(true);\n            setTimeout(()=>{\n                setIsSubmitted(false);\n                setEmail(\"\");\n            }, 3000);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"py-20 lg:py-32 bg-gucci-black text-gucci-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-gucci\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: {\n                            opacity: isInView ? 1 : 0,\n                            y: isInView ? 0 : 50\n                        },\n                        transition: {\n                            duration: 0.8,\n                            ease: [\n                                0.5,\n                                0,\n                                0,\n                                1\n                            ]\n                        },\n                        className: \"mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl lg:text-5xl font-light tracking-wide mb-6\",\n                                children: \"Stay Connected\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg lg:text-xl opacity-90 max-w-2xl mx-auto leading-relaxed\",\n                                children: \"Be the first to know about new collections, exclusive events, and special offers. Join the Gucci community and discover luxury fashion insights.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.form, {\n                        onSubmit: handleSubmit,\n                        className: \"max-w-md mx-auto\",\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        animate: {\n                            opacity: isInView ? 1 : 0,\n                            y: isInView ? 0 : 50\n                        },\n                        transition: {\n                            delay: 0.3,\n                            duration: 0.8,\n                            ease: [\n                                0.5,\n                                0,\n                                0,\n                                1\n                            ]\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.input, {\n                                    type: \"email\",\n                                    value: email,\n                                    onChange: (e)=>setEmail(e.target.value),\n                                    placeholder: \"Enter your email address\",\n                                    className: \"flex-1 px-6 py-4 bg-transparent border border-white/30 text-white placeholder-white/70 focus:outline-none focus:border-white transition-all duration-300\",\n                                    required: true,\n                                    whileFocus: {\n                                        scale: 1.02\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                    type: \"submit\",\n                                    className: \"px-8 py-4 bg-white text-black font-medium tracking-wide uppercase transition-all duration-300 hover:bg-gray-100 disabled:opacity-50\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    disabled: isSubmitted,\n                                    children: isSubmitted ? \"Subscribed!\" : \"Subscribe\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"mt-8 text-sm opacity-70\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: isInView ? 0.7 : 0\n                        },\n                        transition: {\n                            delay: 0.6,\n                            duration: 0.8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"By subscribing, you agree to our\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"underline hover:no-underline transition-all duration-300\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                \" \",\n                                \"and\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"underline hover:no-underline transition-all duration-300\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"mt-16 flex justify-center space-x-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: isInView ? 1 : 0,\n                            y: isInView ? 0 : 30\n                        },\n                        transition: {\n                            delay: 0.8,\n                            duration: 0.8,\n                            ease: [\n                                0.5,\n                                0,\n                                0,\n                                1\n                            ]\n                        },\n                        children: [\n                            \"Instagram\",\n                            \"Facebook\",\n                            \"Twitter\",\n                            \"YouTube\"\n                        ].map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                href: \"#\",\n                                className: \"text-sm font-medium tracking-wide uppercase hover:opacity-70 transition-opacity duration-300\",\n                                whileHover: {\n                                    scale: 1.1\n                                },\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: isInView ? 1 : 0,\n                                    y: isInView ? 0 : 20\n                                },\n                                transition: {\n                                    delay: 0.9 + index * 0.1,\n                                    duration: 0.6\n                                },\n                                children: platform\n                            }, platform, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\NewsletterSection.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewsletterSection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/NewsletterSection.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ProgressIndicator.tsx":
/*!******************************************!*\
  !*** ./components/ProgressIndicator.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ProgressIndicator = ()=>{\n    const [scrollProgress, setScrollProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const totalHeight = document.documentElement.scrollHeight - window.innerHeight;\n            const progress = window.scrollY / totalHeight * 100;\n            setScrollProgress(progress);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-0 left-0 right-0 z-50 h-1 bg-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n            className: \"h-full bg-gucci-black\",\n            style: {\n                width: `${scrollProgress}%`\n            },\n            initial: {\n                width: 0\n            },\n            transition: {\n                duration: 0.1\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\ProgressIndicator.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\ProgressIndicator.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProgressIndicator);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ProgressIndicator.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"de6a218a62cc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ndWNjaS13ZWJzaXRlLWNsb25lLy4vYXBwL2dsb2JhbHMuY3NzP2I5NTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkZTZhMjE4YTYyY2NcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"GUCCI\\xae US Official Site | Redefining Luxury Fashion\",\n    description: \"Discover the latest collections from Gucci. Shop luxury handbags, shoes, ready-to-wear, jewelry, watches, and beauty.\",\n    keywords: \"Gucci, luxury fashion, handbags, shoes, jewelry, watches, beauty\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@100;300;400;500;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} font-gucci antialiased`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\port\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbrady%5CDesktop%5Cport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();