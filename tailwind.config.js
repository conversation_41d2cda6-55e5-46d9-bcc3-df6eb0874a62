/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        'gucci': ['Inter', 'Helvetica', 'Arial', 'sans-serif'],
      },
      colors: {
        'gucci-black': '#000000',
        'gucci-white': '#ffffff',
        'gucci-gold': '#d4af37',
        'gucci-green': '#006341',
        'gucci-red': '#c8102e',
        'gucci-gray': '#f5f5f5',
        'gucci-dark-gray': '#333333',
      },
      animation: {
        'fade-in': 'fadeIn 0.8s cubic-bezier(0.5, 0, 0, 1)',
        'slide-up': 'slideUp 0.8s cubic-bezier(0.5, 0, 0, 1)',
        'slide-in': 'slideIn 0.8s cubic-bezier(0.5, 0, 0, 1)',
        'scale-in': 'scaleIn 0.6s cubic-bezier(0.5, 0, 0, 1)',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)', opacity: '0' },
          '100%': { transform: 'translateY(0%)', opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateX(-100%)', opacity: '0' },
          '100%': { transform: 'translateX(0%)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.8)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      transitionTimingFunction: {
        'gucci': 'cubic-bezier(0.5, 0, 0, 1)',
      },
    },
  },
  plugins: [],
}
