@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;300;400;500;700&display=swap');

:root {
  --g-color-foreground: #000000;
  --g-color-background: #ffffff;
  --g-color-divider: #f5f5f5;
  --g-color-accessibility: #0066cc;
  --g-header-height: 72px;
  --g-header-expanded-height: 120px;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  color: var(--g-color-foreground);
  background: var(--g-color-background);
  font-family: 'Inter', 'Gucci Sans Pro', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

.scroll-lock {
  overflow: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--g-color-divider);
}

::-webkit-scrollbar-thumb {
  background: var(--g-color-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #333;
}

/* Focus styles for accessibility */
@media screen and (min-width: 1024px) {
  *:focus-visible {
    outline: 3px solid var(--g-color-accessibility);
    outline-offset: 2px;
  }
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.8s cubic-bezier(0.5, 0, 0, 1);
}

.animate-slide-up {
  animation: slideUp 0.8s cubic-bezier(0.5, 0, 0, 1);
}

.animate-slide-in {
  animation: slideIn 0.8s cubic-bezier(0.5, 0, 0, 1);
}

.animate-scale-in {
  animation: scaleIn 0.6s cubic-bezier(0.5, 0, 0, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0%);
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0%);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Gucci-specific styles */
.gucci-button {
  @apply bg-gucci-black text-gucci-white px-8 py-3 text-sm font-medium tracking-wide uppercase transition-all duration-300 hover:bg-gucci-dark-gray;
}

.gucci-button-outline {
  @apply border border-gucci-black text-gucci-black px-8 py-3 text-sm font-medium tracking-wide uppercase transition-all duration-300 hover:bg-gucci-black hover:text-gucci-white;
}

.gucci-link {
  @apply text-gucci-black underline decoration-1 underline-offset-4 transition-all duration-300 hover:decoration-2;
}

/* Typography */
.gucci-heading {
  @apply font-light tracking-wide;
}

.gucci-body {
  @apply font-normal leading-relaxed;
}

/* Layout utilities */
.container-gucci {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Image hover effects */
.image-hover-scale {
  @apply transition-transform duration-700 ease-out hover:scale-105;
}

/* Text animations */
.text-reveal {
  @apply opacity-0 translate-y-8 transition-all duration-800 ease-out;
}

.text-reveal.animate {
  @apply opacity-100 translate-y-0;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
