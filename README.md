# Gucci Website Clone

A modern recreation of the Gucci website built with Next.js, React, TypeScript, and Tailwind CSS, featuring smooth animations and responsive design.

## Features

- **Modern Tech Stack**: Built with Next.js 14, React 18, TypeScript, and Tailwind CSS
- **Smooth Animations**: Powered by Framer Motion for elegant transitions and interactions
- **Responsive Design**: Fully responsive across all device sizes
- **Performance Optimized**: Next.js Image optimization and lazy loading
- **Accessibility**: WCAG compliant with proper focus management
- **Luxury Design**: Faithful recreation of Gucci's premium aesthetic

## Components

- **Header**: Responsive navigation with mobile menu and smooth animations
- **Hero**: Full-screen hero section with parallax effects
- **Featured Collections**: Grid layout with hover animations and image overlays
- **Newsletter**: Subscription form with validation and success states
- **Footer**: Comprehensive footer with social links and company information
- **Progress Indicator**: Scroll progress bar for enhanced UX

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd gucci-website-clone
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Technologies Used

- **Next.js 14** - React framework with App Router
- **React 18** - UI library with hooks and modern features
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **Lucide React** - Icon library

## Design Features

- **Typography**: Custom font stack mimicking Gucci's brand typography
- **Color Palette**: Authentic Gucci brand colors (black, white, gold, green, red)
- **Animations**: Smooth page transitions, hover effects, and scroll-triggered animations
- **Layout**: Grid-based responsive design with proper spacing and alignment
- **Images**: High-quality placeholder images with proper optimization

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

This project is for educational purposes only. Gucci is a trademark of Guccio Gucci S.p.A.
