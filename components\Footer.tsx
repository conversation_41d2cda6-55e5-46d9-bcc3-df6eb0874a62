'use client'

import { motion } from 'framer-motion'

const Footer = () => {
  const footerSections = [
    {
      title: 'May We Help You?',
      links: [
        'Contact Us',
        'My Order',
        'FAQs',
        'Email Unsubscribe',
        'Sitemap'
      ]
    },
    {
      title: 'The Company',
      links: [
        'About Gucci',
        'Gucci Equilibrium',
        'Code of Ethics',
        'Careers',
        'Legal',
        'Privacy & Cookie Policy'
      ]
    },
    {
      title: 'Gucci Services',
      links: [
        'Discover Our Services',
        'Book an Appointment',
        'Collect In Store',
        'Personalization',
        'Gift Wrapping'
      ]
    }
  ]

  return (
    <footer className="bg-gucci-black text-gucci-white">
      <div className="container-gucci py-16 lg:py-24">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Brand Section */}
          <motion.div
            className="lg:col-span-1"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: [0.5, 0, 0, 1] }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-light tracking-widest mb-6">GUCCI</h3>
            <p className="text-sm opacity-80 leading-relaxed mb-6">
              Redefining luxury fashion with Italian craftsmanship and contemporary design. 
              Discover our heritage of excellence and innovation.
            </p>
            <div className="flex space-x-4">
              {['IG', 'FB', 'TW', 'YT'].map((social, index) => (
                <motion.a
                  key={social}
                  href="#"
                  className="w-10 h-10 border border-white/30 flex items-center justify-center text-xs font-medium hover:bg-white hover:text-black transition-all duration-300"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  {social}
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Footer Links */}
          {footerSections.map((section, sectionIndex) => (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ 
                delay: (sectionIndex + 1) * 0.2, 
                duration: 0.8, 
                ease: [0.5, 0, 0, 1] 
              }}
              viewport={{ once: true }}
            >
              <h4 className="text-sm font-medium tracking-wide uppercase mb-6 opacity-60">
                {section.title}
              </h4>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <motion.li
                    key={link}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ 
                      delay: (sectionIndex + 1) * 0.2 + linkIndex * 0.1, 
                      duration: 0.6 
                    }}
                    viewport={{ once: true }}
                  >
                    <a
                      href="#"
                      className="text-sm hover:opacity-70 transition-opacity duration-300 block"
                    >
                      {link}
                    </a>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Bottom Section */}
        <motion.div
          className="border-t border-white/20 mt-16 pt-8 flex flex-col lg:flex-row justify-between items-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8, ease: [0.5, 0, 0, 1] }}
          viewport={{ once: true }}
        >
          <div className="text-sm opacity-60 mb-4 lg:mb-0">
            © 2016 - 2025 Guccio Gucci S.p.A. - All rights reserved.
          </div>
          <div className="flex flex-wrap gap-6 text-sm">
            <a href="#" className="hover:opacity-70 transition-opacity duration-300">
              Terms of Service
            </a>
            <a href="#" className="hover:opacity-70 transition-opacity duration-300">
              Privacy Policy
            </a>
            <a href="#" className="hover:opacity-70 transition-opacity duration-300">
              Cookie Settings
            </a>
            <a href="#" className="hover:opacity-70 transition-opacity duration-300">
              Accessibility
            </a>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
