'use client'

import { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import Image from 'next/image'

const FeaturedCollections = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, amount: 0.2 })

  const collections = [
    {
      id: 1,
      title: 'Signature Handbags',
      subtitle: 'Iconic designs reimagined',
      image: 'https://images.unsplash.com/photo-1584917865442-de89df76afd3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1935&q=80',
      link: '#'
    },
    {
      id: 2,
      title: 'Luxury Footwear',
      subtitle: 'Step into elegance',
      image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2012&q=80',
      link: '#'
    },
    {
      id: 3,
      title: 'Fine Jewelry',
      subtitle: 'Timeless luxury pieces',
      image: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      link: '#'
    },
    {
      id: 4,
      title: 'Ready-to-Wear',
      subtitle: 'Contemporary luxury',
      image: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2005&q=80',
      link: '#'
    },
    {
      id: 5,
      title: 'Beauty Collection',
      subtitle: 'Discover Gucci Beauty',
      image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80',
      link: '#'
    },
    {
      id: 6,
      title: 'Watches',
      subtitle: 'Precision meets luxury',
      image: 'https://images.unsplash.com/photo-1524592094714-0f0654e20314?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1999&q=80',
      link: '#'
    }
  ]

  return (
    <section ref={ref} className="py-20 lg:py-32 bg-gucci-white">
      <div className="container-gucci">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 50 }}
          transition={{ duration: 0.8, ease: [0.5, 0, 0, 1] }}
        >
          <h2 className="text-3xl lg:text-5xl font-light tracking-wide mb-4">
            Featured Collections
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Explore our curated selection of luxury pieces that define contemporary elegance
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {collections.map((collection, index) => (
            <motion.div
              key={collection.id}
              className="group cursor-pointer"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 50 }}
              transition={{ 
                delay: index * 0.1, 
                duration: 0.8, 
                ease: [0.5, 0, 0, 1] 
              }}
              whileHover={{ y: -10 }}
            >
              <div className="relative overflow-hidden bg-gray-100 aspect-[4/5] mb-4">
                <Image
                  src={collection.image}
                  alt={collection.title}
                  fill
                  className="object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-500" />
                
                {/* Overlay content */}
                <div className="absolute inset-0 flex items-end p-6">
                  <div className="text-white transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500">
                    <h3 className="text-xl font-medium mb-2">{collection.title}</h3>
                    <p className="text-sm opacity-90 mb-4">{collection.subtitle}</p>
                    <motion.button
                      className="border border-white text-white px-6 py-2 text-sm font-medium tracking-wide uppercase hover:bg-white hover:text-black transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Shop Now
                    </motion.button>
                  </div>
                </div>
              </div>
              
              <div className="text-center">
                <h3 className="text-lg font-medium mb-2 group-hover:opacity-70 transition-opacity duration-300">
                  {collection.title}
                </h3>
                <p className="text-gray-600 text-sm">{collection.subtitle}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FeaturedCollections
