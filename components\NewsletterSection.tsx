'use client'

import { useState, useRef } from 'react'
import { motion, useInView } from 'framer-motion'

const NewsletterSection = () => {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, amount: 0.3 })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      setIsSubmitted(true)
      setTimeout(() => {
        setIsSubmitted(false)
        setEmail('')
      }, 3000)
    }
  }

  return (
    <section ref={ref} className="py-20 lg:py-32 bg-gucci-black text-gucci-white">
      <div className="container-gucci">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 50 }}
            transition={{ duration: 0.8, ease: [0.5, 0, 0, 1] }}
            className="mb-12"
          >
            <h2 className="text-3xl lg:text-5xl font-light tracking-wide mb-6">
              Stay Connected
            </h2>
            <p className="text-lg lg:text-xl opacity-90 max-w-2xl mx-auto leading-relaxed">
              Be the first to know about new collections, exclusive events, and special offers. 
              Join the Gucci community and discover luxury fashion insights.
            </p>
          </motion.div>

          <motion.form
            onSubmit={handleSubmit}
            className="max-w-md mx-auto"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 50 }}
            transition={{ delay: 0.3, duration: 0.8, ease: [0.5, 0, 0, 1] }}
          >
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                className="flex-1 px-6 py-4 bg-transparent border border-white/30 text-white placeholder-white/70 focus:outline-none focus:border-white transition-all duration-300"
                required
                whileFocus={{ scale: 1.02 }}
              />
              <motion.button
                type="submit"
                className="px-8 py-4 bg-white text-black font-medium tracking-wide uppercase transition-all duration-300 hover:bg-gray-100 disabled:opacity-50"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                disabled={isSubmitted}
              >
                {isSubmitted ? 'Subscribed!' : 'Subscribe'}
              </motion.button>
            </div>
          </motion.form>

          <motion.div
            className="mt-8 text-sm opacity-70"
            initial={{ opacity: 0 }}
            animate={{ opacity: isInView ? 0.7 : 0 }}
            transition={{ delay: 0.6, duration: 0.8 }}
          >
            <p>
              By subscribing, you agree to our{' '}
              <a href="#" className="underline hover:no-underline transition-all duration-300">
                Privacy Policy
              </a>{' '}
              and{' '}
              <a href="#" className="underline hover:no-underline transition-all duration-300">
                Terms of Service
              </a>
            </p>
          </motion.div>

          {/* Social Media Links */}
          <motion.div
            className="mt-16 flex justify-center space-x-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 30 }}
            transition={{ delay: 0.8, duration: 0.8, ease: [0.5, 0, 0, 1] }}
          >
            {['Instagram', 'Facebook', 'Twitter', 'YouTube'].map((platform, index) => (
              <motion.a
                key={platform}
                href="#"
                className="text-sm font-medium tracking-wide uppercase hover:opacity-70 transition-opacity duration-300"
                whileHover={{ scale: 1.1 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 20 }}
                transition={{ delay: 0.9 + index * 0.1, duration: 0.6 }}
              >
                {platform}
              </motion.a>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default NewsletterSection
