"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst Header = ()=>{\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 50);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navItems = [\n        \"Women\",\n        \"Men\",\n        \"Handbags\",\n        \"Shoes\",\n        \"Jewelry & Watches\",\n        \"Beauty\",\n        \"Gifts\",\n        \"Gucci Services\",\n        \"World of Gucci\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.header, {\n                className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500 \".concat(isScrolled ? \"bg-gucci-white/95 backdrop-blur-md shadow-sm\" : \"bg-black/20 backdrop-blur-sm\"),\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8,\n                    ease: [\n                        0.5,\n                        0,\n                        0,\n                        1\n                    ]\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-gucci\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 lg:h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"lg:hidden p-2 transition-colors duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                onClick: ()=>setIsMenuOpen(true),\n                                \"aria-label\": \"Open menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"flex-1 lg:flex-none text-center lg:text-left\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl lg:text-3xl font-light tracking-widest transition-colors duration-500 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-lg\"),\n                                    children: \"GUCCI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden lg:flex flex-1 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"flex space-x-8\",\n                                    children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.li, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: index * 0.1,\n                                                duration: 0.6\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm font-medium tracking-wide hover:opacity-70 transition-all duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                                children: item\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, item, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"p-2 hover:opacity-70 transition-all duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        onClick: ()=>setIsSearchOpen(true),\n                                        \"aria-label\": \"Search\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"p-2 hover:opacity-70 transition-all duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        \"aria-label\": \"Wishlist\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"p-2 hover:opacity-70 transition-all duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        \"aria-label\": \"Shopping bag\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"fixed inset-0 z-50 lg:hidden\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/50\",\n                            onClick: ()=>setIsMenuOpen(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute left-0 top-0 h-full w-80 bg-gucci-white\",\n                            initial: {\n                                x: -320\n                            },\n                            animate: {\n                                x: 0\n                            },\n                            exit: {\n                                x: -320\n                            },\n                            transition: {\n                                duration: 0.4,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-light tracking-widest\",\n                                                children: \"GUCCI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsMenuOpen(false),\n                                                className: \"p-2\",\n                                                \"aria-label\": \"Close menu\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-6\",\n                                            children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.li, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1,\n                                                        duration: 0.4\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"block text-lg font-medium tracking-wide hover:opacity-70 transition-opacity duration-300\",\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        children: item\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, item, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: isSearchOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"fixed inset-0 z-50\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        ease: [\n                            0.5,\n                            0,\n                            0,\n                            1\n                        ]\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gucci-black/90 backdrop-blur-sm\",\n                            onClick: ()=>setIsSearchOpen(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute inset-0 flex items-start justify-center pt-32 p-4\",\n                            initial: {\n                                y: -50,\n                                opacity: 0\n                            },\n                            animate: {\n                                y: 0,\n                                opacity: 1\n                            },\n                            exit: {\n                                y: -50,\n                                opacity: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        onClick: ()=>setIsSearchOpen(false),\n                                        className: \"absolute top-8 right-8 p-3 text-white hover:opacity-70 transition-opacity duration-300\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        \"aria-label\": \"Close search\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.input, {\n                                            type: \"text\",\n                                            placeholder: \"Search\",\n                                            className: \"w-full bg-transparent border-0 border-b-2 border-white/30 text-white text-4xl lg:text-6xl font-light tracking-wide placeholder-white/50 focus:outline-none focus:border-white pb-4 transition-all duration-500\",\n                                            autoFocus: true,\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.2,\n                                                duration: 0.6\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        className: \"mb-16\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.4,\n                                            duration: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-white/60 text-sm font-medium uppercase tracking-widest mb-8\",\n                                                children: \"Popular Searches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                                children: [\n                                                    \"Handbags\",\n                                                    \"Sneakers\",\n                                                    \"GG Marmont\",\n                                                    \"Dionysus\",\n                                                    \"Ace Sneakers\",\n                                                    \"Jewelry\",\n                                                    \"Watches\",\n                                                    \"Beauty\"\n                                                ].map((term, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                                        className: \"text-left text-white text-lg font-light tracking-wide hover:opacity-70 transition-opacity duration-300 border-b border-white/20 pb-2\",\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            delay: 0.5 + index * 0.1,\n                                                            duration: 0.4\n                                                        },\n                                                        whileHover: {\n                                                            x: 10\n                                                        },\n                                                        children: term\n                                                    }, term, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.6,\n                                            duration: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-white/60 text-sm font-medium uppercase tracking-widest mb-8\",\n                                                children: \"Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                                children: [\n                                                    {\n                                                        title: \"Women\",\n                                                        items: [\n                                                            \"Ready-to-Wear\",\n                                                            \"Handbags\",\n                                                            \"Shoes\",\n                                                            \"Jewelry\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        title: \"Men\",\n                                                        items: [\n                                                            \"Ready-to-Wear\",\n                                                            \"Bags\",\n                                                            \"Shoes\",\n                                                            \"Accessories\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        title: \"Gifts\",\n                                                        items: [\n                                                            \"For Her\",\n                                                            \"For Him\",\n                                                            \"Beauty\",\n                                                            \"Home\"\n                                                        ]\n                                                    }\n                                                ].map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            delay: 0.7 + categoryIndex * 0.1,\n                                                            duration: 0.4\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-white text-xl font-light tracking-wide mb-4\",\n                                                                children: category.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-2\",\n                                                                children: category.items.map((item, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.li, {\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            x: -10\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            x: 0\n                                                                        },\n                                                                        transition: {\n                                                                            delay: 0.8 + categoryIndex * 0.1 + itemIndex * 0.05,\n                                                                            duration: 0.3\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"#\",\n                                                                            className: \"text-white/70 hover:text-white transition-colors duration-300 text-sm tracking-wide\",\n                                                                            children: item\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, item, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 29\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, category.title, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Header, \"NRlQOkiphvFd0F7IFMLMwg54HfU=\");\n_c = Header;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Header.tsx\n"));

/***/ })

});