"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Menu,Search,ShoppingBag,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst Header = ()=>{\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCartOpen, setIsCartOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 50);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navItems = [\n        \"Women\",\n        \"Men\",\n        \"Handbags\",\n        \"Shoes\",\n        \"Jewelry & Watches\",\n        \"Beauty\",\n        \"Gifts\",\n        \"Gucci Services\",\n        \"World of Gucci\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.header, {\n                className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500 \".concat(isScrolled ? \"bg-gucci-white/95 backdrop-blur-md shadow-sm\" : \"bg-black/20 backdrop-blur-sm\"),\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.8,\n                    ease: [\n                        0.5,\n                        0,\n                        0,\n                        1\n                    ]\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-gucci\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 lg:h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"lg:hidden p-2 transition-colors duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                onClick: ()=>setIsMenuOpen(true),\n                                \"aria-label\": \"Open menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    size: 24\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                className: \"flex-1 lg:flex-none text-center lg:text-left\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl lg:text-3xl font-light tracking-widest transition-colors duration-500 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-lg\"),\n                                    children: \"GUCCI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden lg:flex flex-1 justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"flex space-x-8\",\n                                    children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.li, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: index * 0.1,\n                                                duration: 0.6\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                className: \"text-sm font-medium tracking-wide hover:opacity-70 transition-all duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                                children: item\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, item, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"p-2 hover:opacity-70 transition-all duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        onClick: ()=>setIsSearchOpen(true),\n                                        \"aria-label\": \"Search\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"p-2 hover:opacity-70 transition-all duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        \"aria-label\": \"Wishlist\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        className: \"p-2 hover:opacity-70 transition-all duration-300 \".concat(isScrolled ? \"text-gucci-black\" : \"text-white drop-shadow-md\"),\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        onClick: ()=>setIsCartOpen(true),\n                                        \"aria-label\": \"Shopping bag\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"fixed inset-0 z-50 lg:hidden\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/50\",\n                            onClick: ()=>setIsMenuOpen(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute left-0 top-0 h-full w-80 bg-gucci-white\",\n                            initial: {\n                                x: -320\n                            },\n                            animate: {\n                                x: 0\n                            },\n                            exit: {\n                                x: -320\n                            },\n                            transition: {\n                                duration: 0.4,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-light tracking-widest\",\n                                                children: \"GUCCI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsMenuOpen(false),\n                                                className: \"p-2\",\n                                                \"aria-label\": \"Close menu\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-6\",\n                                            children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.li, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: -20\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1,\n                                                        duration: 0.4\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"block text-lg font-medium tracking-wide hover:opacity-70 transition-opacity duration-300\",\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        children: item\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, item, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: isSearchOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"fixed inset-0 z-50\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        ease: [\n                            0.5,\n                            0,\n                            0,\n                            1\n                        ]\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gucci-black/90 backdrop-blur-sm\",\n                            onClick: ()=>setIsSearchOpen(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute inset-0 flex items-start justify-center pt-32 p-4\",\n                            initial: {\n                                y: -50,\n                                opacity: 0\n                            },\n                            animate: {\n                                y: 0,\n                                opacity: 1\n                            },\n                            exit: {\n                                y: -50,\n                                opacity: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full max-w-4xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                        onClick: ()=>setIsSearchOpen(false),\n                                        className: \"absolute top-8 right-8 p-3 text-white hover:opacity-70 transition-opacity duration-300\",\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        \"aria-label\": \"Close search\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.input, {\n                                            type: \"text\",\n                                            placeholder: \"Search\",\n                                            className: \"w-full bg-transparent border-0 border-b-2 border-white/30 text-white text-4xl lg:text-6xl font-light tracking-wide placeholder-white/50 focus:outline-none focus:border-white pb-4 transition-all duration-500\",\n                                            autoFocus: true,\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.2,\n                                                duration: 0.6\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        className: \"mb-16\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.4,\n                                            duration: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-white/60 text-sm font-medium uppercase tracking-widest mb-8\",\n                                                children: \"Popular Searches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                                                children: [\n                                                    \"Handbags\",\n                                                    \"Sneakers\",\n                                                    \"GG Marmont\",\n                                                    \"Dionysus\",\n                                                    \"Ace Sneakers\",\n                                                    \"Jewelry\",\n                                                    \"Watches\",\n                                                    \"Beauty\"\n                                                ].map((term, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                                        className: \"text-left text-white text-lg font-light tracking-wide hover:opacity-70 transition-opacity duration-300 border-b border-white/20 pb-2\",\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            delay: 0.5 + index * 0.1,\n                                                            duration: 0.4\n                                                        },\n                                                        whileHover: {\n                                                            x: 10\n                                                        },\n                                                        children: term\n                                                    }, term, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.6,\n                                            duration: 0.6\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-white/60 text-sm font-medium uppercase tracking-widest mb-8\",\n                                                children: \"Categories\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                                children: [\n                                                    {\n                                                        title: \"Women\",\n                                                        items: [\n                                                            \"Ready-to-Wear\",\n                                                            \"Handbags\",\n                                                            \"Shoes\",\n                                                            \"Jewelry\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        title: \"Men\",\n                                                        items: [\n                                                            \"Ready-to-Wear\",\n                                                            \"Bags\",\n                                                            \"Shoes\",\n                                                            \"Accessories\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        title: \"Gifts\",\n                                                        items: [\n                                                            \"For Her\",\n                                                            \"For Him\",\n                                                            \"Beauty\",\n                                                            \"Home\"\n                                                        ]\n                                                    }\n                                                ].map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            delay: 0.7 + categoryIndex * 0.1,\n                                                            duration: 0.4\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-white text-xl font-light tracking-wide mb-4\",\n                                                                children: category.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-2\",\n                                                                children: category.items.map((item, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.li, {\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            x: -10\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            x: 0\n                                                                        },\n                                                                        transition: {\n                                                                            delay: 0.8 + categoryIndex * 0.1 + itemIndex * 0.05,\n                                                                            duration: 0.3\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                            href: \"#\",\n                                                                            className: \"text-white/70 hover:text-white transition-colors duration-300 text-sm tracking-wide\",\n                                                                            children: item\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                            lineNumber: 295,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, item, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 29\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, category.title, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: isCartOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"fixed inset-0 z-50\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        ease: [\n                            0.5,\n                            0,\n                            0,\n                            1\n                        ]\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gucci-black/90 backdrop-blur-sm\",\n                            onClick: ()=>setIsCartOpen(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"absolute right-0 top-0 h-full w-full max-w-md bg-gucci-white shadow-2xl\",\n                            initial: {\n                                x: \"100%\"\n                            },\n                            animate: {\n                                x: 0\n                            },\n                            exit: {\n                                x: \"100%\"\n                            },\n                            transition: {\n                                duration: 0.6,\n                                ease: [\n                                    0.5,\n                                    0,\n                                    0,\n                                    1\n                                ]\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-6 border-b border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-light tracking-wide\",\n                                            children: \"Shopping Bag\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsCartOpen(false),\n                                            className: \"p-2 hover:opacity-70 transition-opacity duration-300\",\n                                            \"aria-label\": \"Close cart\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Menu_Search_ShoppingBag_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-y-auto p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            {\n                                                id: 1,\n                                                name: \"GG Marmont Small Matelass\\xe9 Shoulder Bag\",\n                                                price: \"$2,100\",\n                                                color: \"Black\",\n                                                size: \"Small\",\n                                                image: \"https://images.unsplash.com/photo-1584917865442-de89df76afd3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n                                                quantity: 1\n                                            },\n                                            {\n                                                id: 2,\n                                                name: \"Ace Leather Sneaker\",\n                                                price: \"$650\",\n                                                color: \"White\",\n                                                size: \"9\",\n                                                image: \"https://images.unsplash.com/photo-1549298916-b41d501d3772?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80\",\n                                                quantity: 1\n                                            }\n                                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                className: \"flex space-x-4 pb-6 border-b border-gray-100 last:border-b-0\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: index * 0.1,\n                                                    duration: 0.4\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-20 h-20 bg-gray-100 rounded overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: item.image,\n                                                            alt: item.name,\n                                                            className: \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-sm mb-1 leading-tight\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 mb-2\",\n                                                                children: [\n                                                                    item.color,\n                                                                    \" • Size \",\n                                                                    item.size\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: item.price\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"w-6 h-6 border border-gray-300 flex items-center justify-center text-xs hover:bg-gray-100 transition-colors duration-200\",\n                                                                                children: \"-\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                                lineNumber: 393,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: item.quantity\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                                lineNumber: 396,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"w-6 h-6 border border-gray-300 flex items-center justify-center text-xs hover:bg-gray-100 transition-colors duration-200\",\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                                lineNumber: 397,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-100 p-6 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center text-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-light tracking-wide\",\n                                                    children: \"Subtotal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"$2,750\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: \"Shipping and taxes calculated at checkout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                            className: \"w-full bg-gucci-black text-white py-4 text-sm font-medium tracking-wide uppercase transition-all duration-300 hover:bg-gucci-dark-gray\",\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            whileTap: {\n                                                scale: 0.98\n                                            },\n                                            children: \"Proceed to Checkout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full text-center text-sm tracking-wide hover:opacity-70 transition-opacity duration-300\",\n                                            onClick: ()=>setIsCartOpen(false),\n                                            children: \"Continue Shopping\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\port\\\\components\\\\Header.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Header, \"EpAU2WI+eRG9kSNC3J6G99WMNZM=\");\n_c = Header;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ1k7QUFDVztBQUVsRSxNQUFNUyxTQUFTOztJQUNiLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHWCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNZLFlBQVlDLGNBQWMsR0FBR2IsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDYyxjQUFjQyxnQkFBZ0IsR0FBR2YsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDZ0IsWUFBWUMsY0FBYyxHQUFHakIsK0NBQVFBLENBQUM7SUFFN0NDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWlCLGVBQWU7WUFDbkJMLGNBQWNNLE9BQU9DLE9BQU8sR0FBRztRQUNqQztRQUVBRCxPQUFPRSxnQkFBZ0IsQ0FBQyxVQUFVSDtRQUNsQyxPQUFPLElBQU1DLE9BQU9HLG1CQUFtQixDQUFDLFVBQVVKO0lBQ3BELEdBQUcsRUFBRTtJQUVMLE1BQU1LLFdBQVc7UUFDZjtRQUFTO1FBQU87UUFBWTtRQUFTO1FBQ3JDO1FBQVU7UUFBUztRQUFrQjtLQUN0QztJQUVELHFCQUNFOzswQkFDRSw4REFBQ3JCLGlEQUFNQSxDQUFDc0IsTUFBTTtnQkFDWkMsV0FBVywrREFJVixPQUhDYixhQUNJLGlEQUNBO2dCQUVOYyxTQUFTO29CQUFFQyxHQUFHLENBQUM7Z0JBQUk7Z0JBQ25CQyxTQUFTO29CQUFFRCxHQUFHO2dCQUFFO2dCQUNoQkUsWUFBWTtvQkFBRUMsVUFBVTtvQkFBS0MsTUFBTTt3QkFBQzt3QkFBSzt3QkFBRzt3QkFBRztxQkFBRTtnQkFBQzswQkFFbEQsNEVBQUNDO29CQUFJUCxXQUFVOzhCQUNiLDRFQUFDTzt3QkFBSVAsV0FBVTs7MENBRWIsOERBQUNRO2dDQUNDUixXQUFXLGdEQUVWLE9BRENiLGFBQWEscUJBQXFCO2dDQUVwQ3NCLFNBQVMsSUFBTXZCLGNBQWM7Z0NBQzdCd0IsY0FBVzswQ0FFWCw0RUFBQzVCLDJHQUFJQTtvQ0FBQzZCLE1BQU07Ozs7Ozs7Ozs7OzBDQUlkLDhEQUFDbEMsaURBQU1BLENBQUM4QixHQUFHO2dDQUNUUCxXQUFVO2dDQUNWWSxZQUFZO29DQUFFQyxPQUFPO2dDQUFLO2dDQUMxQlQsWUFBWTtvQ0FBRUMsVUFBVTtnQ0FBSTswQ0FFNUIsNEVBQUNTO29DQUFHZCxXQUFXLGtGQUVkLE9BRENiLGFBQWEscUJBQXFCOzhDQUNoQzs7Ozs7Ozs7Ozs7MENBTU4sOERBQUM0QjtnQ0FBSWYsV0FBVTswQ0FDYiw0RUFBQ2dCO29DQUFHaEIsV0FBVTs4Q0FDWEYsU0FBU21CLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDbkIsOERBQUMxQyxpREFBTUEsQ0FBQzJDLEVBQUU7NENBRVJuQixTQUFTO2dEQUFFb0IsU0FBUztnREFBR25CLEdBQUc7NENBQUc7NENBQzdCQyxTQUFTO2dEQUFFa0IsU0FBUztnREFBR25CLEdBQUc7NENBQUU7NENBQzVCRSxZQUFZO2dEQUFFa0IsT0FBT0gsUUFBUTtnREFBS2QsVUFBVTs0Q0FBSTtzREFFaEQsNEVBQUNrQjtnREFDQ0MsTUFBSztnREFDTHhCLFdBQVcsa0ZBRVYsT0FEQ2IsYUFBYSxxQkFBcUI7MERBR25DK0I7Ozs7OzsyQ0FYRUE7Ozs7Ozs7Ozs7Ozs7OzswQ0FtQmIsOERBQUNYO2dDQUFJUCxXQUFVOztrREFDYiw4REFBQ3ZCLGlEQUFNQSxDQUFDK0IsTUFBTTt3Q0FDWlIsV0FBVyxvREFFVixPQURDYixhQUFhLHFCQUFxQjt3Q0FFcEN5QixZQUFZOzRDQUFFQyxPQUFPO3dDQUFJO3dDQUN6QlksVUFBVTs0Q0FBRVosT0FBTzt3Q0FBSzt3Q0FDeEJKLFNBQVMsSUFBTW5CLGdCQUFnQjt3Q0FDL0JvQixjQUFXO2tEQUVYLDRFQUFDL0IsMkdBQU1BOzRDQUFDZ0MsTUFBTTs7Ozs7Ozs7Ozs7a0RBRWhCLDhEQUFDbEMsaURBQU1BLENBQUMrQixNQUFNO3dDQUNaUixXQUFXLG9EQUVWLE9BRENiLGFBQWEscUJBQXFCO3dDQUVwQ3lCLFlBQVk7NENBQUVDLE9BQU87d0NBQUk7d0NBQ3pCWSxVQUFVOzRDQUFFWixPQUFPO3dDQUFLO3dDQUN4QkgsY0FBVztrREFFWCw0RUFBQzlCLDJHQUFLQTs0Q0FBQytCLE1BQU07Ozs7Ozs7Ozs7O2tEQUVmLDhEQUFDbEMsaURBQU1BLENBQUMrQixNQUFNO3dDQUNaUixXQUFXLG9EQUVWLE9BRENiLGFBQWEscUJBQXFCO3dDQUVwQ3lCLFlBQVk7NENBQUVDLE9BQU87d0NBQUk7d0NBQ3pCWSxVQUFVOzRDQUFFWixPQUFPO3dDQUFLO3dDQUN4QkosU0FBUyxJQUFNakIsY0FBYzt3Q0FDN0JrQixjQUFXO2tEQUVYLDRFQUFDN0IsMkdBQVdBOzRDQUFDOEIsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVE3Qiw4REFBQ2pDLDBEQUFlQTswQkFDYk8sNEJBQ0MsOERBQUNSLGlEQUFNQSxDQUFDOEIsR0FBRztvQkFDVFAsV0FBVTtvQkFDVkMsU0FBUzt3QkFBRW9CLFNBQVM7b0JBQUU7b0JBQ3RCbEIsU0FBUzt3QkFBRWtCLFNBQVM7b0JBQUU7b0JBQ3RCSyxNQUFNO3dCQUFFTCxTQUFTO29CQUFFO29CQUNuQmpCLFlBQVk7d0JBQUVDLFVBQVU7b0JBQUk7O3NDQUU1Qiw4REFBQ0U7NEJBQUlQLFdBQVU7NEJBQStCUyxTQUFTLElBQU12QixjQUFjOzs7Ozs7c0NBQzNFLDhEQUFDVCxpREFBTUEsQ0FBQzhCLEdBQUc7NEJBQ1RQLFdBQVU7NEJBQ1ZDLFNBQVM7Z0NBQUUwQixHQUFHLENBQUM7NEJBQUk7NEJBQ25CeEIsU0FBUztnQ0FBRXdCLEdBQUc7NEJBQUU7NEJBQ2hCRCxNQUFNO2dDQUFFQyxHQUFHLENBQUM7NEJBQUk7NEJBQ2hCdkIsWUFBWTtnQ0FBRUMsVUFBVTtnQ0FBS0MsTUFBTTtvQ0FBQztvQ0FBSztvQ0FBRztvQ0FBRztpQ0FBRTs0QkFBQztzQ0FFbEQsNEVBQUNDO2dDQUFJUCxXQUFVOztrREFDYiw4REFBQ087d0NBQUlQLFdBQVU7OzBEQUNiLDhEQUFDNEI7Z0RBQUc1QixXQUFVOzBEQUFzQzs7Ozs7OzBEQUNwRCw4REFBQ1E7Z0RBQ0NDLFNBQVMsSUFBTXZCLGNBQWM7Z0RBQzdCYyxXQUFVO2dEQUNWVSxjQUFXOzBEQUVYLDRFQUFDM0IsMkdBQUNBO29EQUFDNEIsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR2IsOERBQUNJO2tEQUNDLDRFQUFDQzs0Q0FBR2hCLFdBQVU7c0RBQ1hGLFNBQVNtQixHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQ25CLDhEQUFDMUMsaURBQU1BLENBQUMyQyxFQUFFO29EQUVSbkIsU0FBUzt3REFBRW9CLFNBQVM7d0RBQUdNLEdBQUcsQ0FBQztvREFBRztvREFDOUJ4QixTQUFTO3dEQUFFa0IsU0FBUzt3REFBR00sR0FBRztvREFBRTtvREFDNUJ2QixZQUFZO3dEQUFFa0IsT0FBT0gsUUFBUTt3REFBS2QsVUFBVTtvREFBSTs4REFFaEQsNEVBQUNrQjt3REFDQ0MsTUFBSzt3REFDTHhCLFdBQVU7d0RBQ1ZTLFNBQVMsSUFBTXZCLGNBQWM7a0VBRTVCZ0M7Ozs7OzttREFWRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBdUJ2Qiw4REFBQ3hDLDBEQUFlQTswQkFDYlcsOEJBQ0MsOERBQUNaLGlEQUFNQSxDQUFDOEIsR0FBRztvQkFDVFAsV0FBVTtvQkFDVkMsU0FBUzt3QkFBRW9CLFNBQVM7b0JBQUU7b0JBQ3RCbEIsU0FBUzt3QkFBRWtCLFNBQVM7b0JBQUU7b0JBQ3RCSyxNQUFNO3dCQUFFTCxTQUFTO29CQUFFO29CQUNuQmpCLFlBQVk7d0JBQUVDLFVBQVU7d0JBQUtDLE1BQU07NEJBQUM7NEJBQUs7NEJBQUc7NEJBQUc7eUJBQUU7b0JBQUM7O3NDQUVsRCw4REFBQ0M7NEJBQ0NQLFdBQVU7NEJBQ1ZTLFNBQVMsSUFBTW5CLGdCQUFnQjs7Ozs7O3NDQUVqQyw4REFBQ2IsaURBQU1BLENBQUM4QixHQUFHOzRCQUNUUCxXQUFVOzRCQUNWQyxTQUFTO2dDQUFFQyxHQUFHLENBQUM7Z0NBQUltQixTQUFTOzRCQUFFOzRCQUM5QmxCLFNBQVM7Z0NBQUVELEdBQUc7Z0NBQUdtQixTQUFTOzRCQUFFOzRCQUM1QkssTUFBTTtnQ0FBRXhCLEdBQUcsQ0FBQztnQ0FBSW1CLFNBQVM7NEJBQUU7NEJBQzNCakIsWUFBWTtnQ0FBRUMsVUFBVTtnQ0FBS0MsTUFBTTtvQ0FBQztvQ0FBSztvQ0FBRztvQ0FBRztpQ0FBRTs0QkFBQztzQ0FFbEQsNEVBQUNDO2dDQUFJUCxXQUFVOztrREFFYiw4REFBQ3ZCLGlEQUFNQSxDQUFDK0IsTUFBTTt3Q0FDWkMsU0FBUyxJQUFNbkIsZ0JBQWdCO3dDQUMvQlUsV0FBVTt3Q0FDVlksWUFBWTs0Q0FBRUMsT0FBTzt3Q0FBSTt3Q0FDekJZLFVBQVU7NENBQUVaLE9BQU87d0NBQUs7d0NBQ3hCSCxjQUFXO2tEQUVYLDRFQUFDM0IsMkdBQUNBOzRDQUFDNEIsTUFBTTs7Ozs7Ozs7Ozs7a0RBSVgsOERBQUNKO3dDQUFJUCxXQUFVO2tEQUNiLDRFQUFDdkIsaURBQU1BLENBQUNvRCxLQUFLOzRDQUNYQyxNQUFLOzRDQUNMQyxhQUFZOzRDQUNaL0IsV0FBVTs0Q0FDVmdDLFNBQVM7NENBQ1QvQixTQUFTO2dEQUFFb0IsU0FBUztnREFBR25CLEdBQUc7NENBQUc7NENBQzdCQyxTQUFTO2dEQUFFa0IsU0FBUztnREFBR25CLEdBQUc7NENBQUU7NENBQzVCRSxZQUFZO2dEQUFFa0IsT0FBTztnREFBS2pCLFVBQVU7NENBQUk7Ozs7Ozs7Ozs7O2tEQUs1Qyw4REFBQzVCLGlEQUFNQSxDQUFDOEIsR0FBRzt3Q0FDVFAsV0FBVTt3Q0FDVkMsU0FBUzs0Q0FBRW9CLFNBQVM7NENBQUduQixHQUFHO3dDQUFHO3dDQUM3QkMsU0FBUzs0Q0FBRWtCLFNBQVM7NENBQUduQixHQUFHO3dDQUFFO3dDQUM1QkUsWUFBWTs0Q0FBRWtCLE9BQU87NENBQUtqQixVQUFVO3dDQUFJOzswREFFeEMsOERBQUM0QjtnREFBR2pDLFdBQVU7MERBQW1FOzs7Ozs7MERBR2pGLDhEQUFDTztnREFBSVAsV0FBVTswREFDWjtvREFDQztvREFDQTtvREFDQTtvREFDQTtvREFDQTtvREFDQTtvREFDQTtvREFDQTtpREFDRCxDQUFDaUIsR0FBRyxDQUFDLENBQUNpQixNQUFNZixzQkFDWCw4REFBQzFDLGlEQUFNQSxDQUFDK0IsTUFBTTt3REFFWlIsV0FBVTt3REFDVkMsU0FBUzs0REFBRW9CLFNBQVM7NERBQUdNLEdBQUcsQ0FBQzt3REFBRzt3REFDOUJ4QixTQUFTOzREQUFFa0IsU0FBUzs0REFBR00sR0FBRzt3REFBRTt3REFDNUJ2QixZQUFZOzREQUFFa0IsT0FBTyxNQUFNSCxRQUFROzREQUFLZCxVQUFVO3dEQUFJO3dEQUN0RE8sWUFBWTs0REFBRWUsR0FBRzt3REFBRztrRUFFbkJPO3VEQVBJQTs7Ozs7Ozs7Ozs7Ozs7OztrREFjYiw4REFBQ3pELGlEQUFNQSxDQUFDOEIsR0FBRzt3Q0FDVE4sU0FBUzs0Q0FBRW9CLFNBQVM7NENBQUduQixHQUFHO3dDQUFHO3dDQUM3QkMsU0FBUzs0Q0FBRWtCLFNBQVM7NENBQUduQixHQUFHO3dDQUFFO3dDQUM1QkUsWUFBWTs0Q0FBRWtCLE9BQU87NENBQUtqQixVQUFVO3dDQUFJOzswREFFeEMsOERBQUM0QjtnREFBR2pDLFdBQVU7MERBQW1FOzs7Ozs7MERBR2pGLDhEQUFDTztnREFBSVAsV0FBVTswREFDWjtvREFDQzt3REFBRW1DLE9BQU87d0RBQVNDLE9BQU87NERBQUM7NERBQWlCOzREQUFZOzREQUFTO3lEQUFVO29EQUFDO29EQUMzRTt3REFBRUQsT0FBTzt3REFBT0MsT0FBTzs0REFBQzs0REFBaUI7NERBQVE7NERBQVM7eURBQWM7b0RBQUM7b0RBQ3pFO3dEQUFFRCxPQUFPO3dEQUFTQyxPQUFPOzREQUFDOzREQUFXOzREQUFXOzREQUFVO3lEQUFPO29EQUFDO2lEQUNuRSxDQUFDbkIsR0FBRyxDQUFDLENBQUNvQixVQUFVQyw4QkFDZiw4REFBQzdELGlEQUFNQSxDQUFDOEIsR0FBRzt3REFFVE4sU0FBUzs0REFBRW9CLFNBQVM7NERBQUduQixHQUFHO3dEQUFHO3dEQUM3QkMsU0FBUzs0REFBRWtCLFNBQVM7NERBQUduQixHQUFHO3dEQUFFO3dEQUM1QkUsWUFBWTs0REFBRWtCLE9BQU8sTUFBTWdCLGdCQUFnQjs0REFBS2pDLFVBQVU7d0RBQUk7OzBFQUU5RCw4REFBQ2tDO2dFQUFHdkMsV0FBVTswRUFDWHFDLFNBQVNGLEtBQUs7Ozs7OzswRUFFakIsOERBQUNuQjtnRUFBR2hCLFdBQVU7MEVBQ1hxQyxTQUFTRCxLQUFLLENBQUNuQixHQUFHLENBQUMsQ0FBQ0MsTUFBTXNCLDBCQUN6Qiw4REFBQy9ELGlEQUFNQSxDQUFDMkMsRUFBRTt3RUFFUm5CLFNBQVM7NEVBQUVvQixTQUFTOzRFQUFHTSxHQUFHLENBQUM7d0VBQUc7d0VBQzlCeEIsU0FBUzs0RUFBRWtCLFNBQVM7NEVBQUdNLEdBQUc7d0VBQUU7d0VBQzVCdkIsWUFBWTs0RUFBRWtCLE9BQU8sTUFBTWdCLGdCQUFnQixNQUFNRSxZQUFZOzRFQUFNbkMsVUFBVTt3RUFBSTtrRkFFakYsNEVBQUNrQjs0RUFDQ0MsTUFBSzs0RUFDTHhCLFdBQVU7c0ZBRVRrQjs7Ozs7O3VFQVRFQTs7Ozs7Ozs7Ozs7dURBWE5tQixTQUFTRixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFvQ3JDLDhEQUFDekQsMERBQWVBOzBCQUNiYSw0QkFDQyw4REFBQ2QsaURBQU1BLENBQUM4QixHQUFHO29CQUNUUCxXQUFVO29CQUNWQyxTQUFTO3dCQUFFb0IsU0FBUztvQkFBRTtvQkFDdEJsQixTQUFTO3dCQUFFa0IsU0FBUztvQkFBRTtvQkFDdEJLLE1BQU07d0JBQUVMLFNBQVM7b0JBQUU7b0JBQ25CakIsWUFBWTt3QkFBRUMsVUFBVTt3QkFBS0MsTUFBTTs0QkFBQzs0QkFBSzs0QkFBRzs0QkFBRzt5QkFBRTtvQkFBQzs7c0NBRWxELDhEQUFDQzs0QkFDQ1AsV0FBVTs0QkFDVlMsU0FBUyxJQUFNakIsY0FBYzs7Ozs7O3NDQUUvQiw4REFBQ2YsaURBQU1BLENBQUM4QixHQUFHOzRCQUNUUCxXQUFVOzRCQUNWQyxTQUFTO2dDQUFFMEIsR0FBRzs0QkFBTzs0QkFDckJ4QixTQUFTO2dDQUFFd0IsR0FBRzs0QkFBRTs0QkFDaEJELE1BQU07Z0NBQUVDLEdBQUc7NEJBQU87NEJBQ2xCdkIsWUFBWTtnQ0FBRUMsVUFBVTtnQ0FBS0MsTUFBTTtvQ0FBQztvQ0FBSztvQ0FBRztvQ0FBRztpQ0FBRTs0QkFBQzs7OENBR2xELDhEQUFDQztvQ0FBSVAsV0FBVTs7c0RBQ2IsOERBQUN5Qzs0Q0FBR3pDLFdBQVU7c0RBQW1DOzs7Ozs7c0RBQ2pELDhEQUFDUTs0Q0FDQ0MsU0FBUyxJQUFNakIsY0FBYzs0Q0FDN0JRLFdBQVU7NENBQ1ZVLGNBQVc7c0RBRVgsNEVBQUMzQiwyR0FBQ0E7Z0RBQUM0QixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLYiw4REFBQ0o7b0NBQUlQLFdBQVU7OENBRWIsNEVBQUNPO3dDQUFJUCxXQUFVO2tEQUNaOzRDQUNDO2dEQUNFMEMsSUFBSTtnREFDSkMsTUFBTTtnREFDTkMsT0FBTztnREFDUEMsT0FBTztnREFDUGxDLE1BQU07Z0RBQ05tQyxPQUFPO2dEQUNQQyxVQUFVOzRDQUNaOzRDQUNBO2dEQUNFTCxJQUFJO2dEQUNKQyxNQUFNO2dEQUNOQyxPQUFPO2dEQUNQQyxPQUFPO2dEQUNQbEMsTUFBTTtnREFDTm1DLE9BQU87Z0RBQ1BDLFVBQVU7NENBQ1o7eUNBQ0QsQ0FBQzlCLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDWCw4REFBQzFDLGlEQUFNQSxDQUFDOEIsR0FBRztnREFFVFAsV0FBVTtnREFDVkMsU0FBUztvREFBRW9CLFNBQVM7b0RBQUduQixHQUFHO2dEQUFHO2dEQUM3QkMsU0FBUztvREFBRWtCLFNBQVM7b0RBQUduQixHQUFHO2dEQUFFO2dEQUM1QkUsWUFBWTtvREFBRWtCLE9BQU9ILFFBQVE7b0RBQUtkLFVBQVU7Z0RBQUk7O2tFQUVoRCw4REFBQ0U7d0RBQUlQLFdBQVU7a0VBQ2IsNEVBQUNnRDs0REFDQ0MsS0FBSy9CLEtBQUs0QixLQUFLOzREQUNmSSxLQUFLaEMsS0FBS3lCLElBQUk7NERBQ2QzQyxXQUFVOzs7Ozs7Ozs7OztrRUFHZCw4REFBQ087d0RBQUlQLFdBQVU7OzBFQUNiLDhEQUFDaUM7Z0VBQUdqQyxXQUFVOzBFQUEwQ2tCLEtBQUt5QixJQUFJOzs7Ozs7MEVBQ2pFLDhEQUFDUTtnRUFBRW5ELFdBQVU7O29FQUNWa0IsS0FBSzJCLEtBQUs7b0VBQUM7b0VBQVMzQixLQUFLUCxJQUFJOzs7Ozs7OzBFQUVoQyw4REFBQ0o7Z0VBQUlQLFdBQVU7O2tGQUNiLDhEQUFDb0Q7d0VBQUtwRCxXQUFVO2tGQUFla0IsS0FBSzBCLEtBQUs7Ozs7OztrRkFDekMsOERBQUNyQzt3RUFBSVAsV0FBVTs7MEZBQ2IsOERBQUNRO2dGQUFPUixXQUFVOzBGQUEySDs7Ozs7OzBGQUc3SSw4REFBQ29EO2dGQUFLcEQsV0FBVTswRkFBV2tCLEtBQUs2QixRQUFROzs7Ozs7MEZBQ3hDLDhEQUFDdkM7Z0ZBQU9SLFdBQVU7MEZBQTJIOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytDQXpCOUlrQixLQUFLd0IsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzhDQTJEcEIsOERBQUNuQztvQ0FBSVAsV0FBVTs7c0RBRWIsOERBQUNPOzRDQUFJUCxXQUFVOzs4REFDYiw4REFBQ29EO29EQUFLcEQsV0FBVTs4REFBMkI7Ozs7Ozs4REFDM0MsOERBQUNvRDtvREFBS3BELFdBQVU7OERBQWM7Ozs7Ozs7Ozs7OztzREFJaEMsOERBQUNtRDs0Q0FBRW5ELFdBQVU7c0RBQXdCOzs7Ozs7c0RBS3JDLDhEQUFDdkIsaURBQU1BLENBQUMrQixNQUFNOzRDQUNaUixXQUFVOzRDQUNWWSxZQUFZO2dEQUFFQyxPQUFPOzRDQUFLOzRDQUMxQlksVUFBVTtnREFBRVosT0FBTzs0Q0FBSztzREFDekI7Ozs7OztzREFLRCw4REFBQ0w7NENBQ0NSLFdBQVU7NENBQ1ZTLFNBQVMsSUFBTWpCLGNBQWM7c0RBQzlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWpCO0dBM2NNUjtLQUFBQTtBQTZjTiwrREFBZUEsTUFBTUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL0hlYWRlci50c3g/MDM2OCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuaW1wb3J0IHsgU2VhcmNoLCBIZWFydCwgU2hvcHBpbmdCYWcsIE1lbnUsIFggfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmNvbnN0IEhlYWRlciA9ICgpID0+IHtcbiAgY29uc3QgW2lzTWVudU9wZW4sIHNldElzTWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc1Njcm9sbGVkLCBzZXRJc1Njcm9sbGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaXNTZWFyY2hPcGVuLCBzZXRJc1NlYXJjaE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc0NhcnRPcGVuLCBzZXRJc0NhcnRPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlU2Nyb2xsID0gKCkgPT4ge1xuICAgICAgc2V0SXNTY3JvbGxlZCh3aW5kb3cuc2Nyb2xsWSA+IDUwKVxuICAgIH1cblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpXG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IG5hdkl0ZW1zID0gW1xuICAgICdXb21lbicsICdNZW4nLCAnSGFuZGJhZ3MnLCAnU2hvZXMnLCAnSmV3ZWxyeSAmIFdhdGNoZXMnLCBcbiAgICAnQmVhdXR5JywgJ0dpZnRzJywgJ0d1Y2NpIFNlcnZpY2VzJywgJ1dvcmxkIG9mIEd1Y2NpJ1xuICBdXG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPG1vdGlvbi5oZWFkZXJcbiAgICAgICAgY2xhc3NOYW1lPXtgZml4ZWQgdG9wLTAgbGVmdC0wIHJpZ2h0LTAgei01MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgJHtcbiAgICAgICAgICBpc1Njcm9sbGVkXG4gICAgICAgICAgICA/ICdiZy1ndWNjaS13aGl0ZS85NSBiYWNrZHJvcC1ibHVyLW1kIHNoYWRvdy1zbSdcbiAgICAgICAgICAgIDogJ2JnLWJsYWNrLzIwIGJhY2tkcm9wLWJsdXItc20nXG4gICAgICAgIH1gfVxuICAgICAgICBpbml0aWFsPXt7IHk6IC0xMDAgfX1cbiAgICAgICAgYW5pbWF0ZT17eyB5OiAwIH19XG4gICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCwgZWFzZTogWzAuNSwgMCwgMCwgMV0gfX1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXItZ3VjY2lcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBoLTE2IGxnOmgtMjBcIj5cbiAgICAgICAgICAgIHsvKiBNb2JpbGUgTWVudSBCdXR0b24gKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGxnOmhpZGRlbiBwLTIgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgaXNTY3JvbGxlZCA/ICd0ZXh0LWd1Y2NpLWJsYWNrJyA6ICd0ZXh0LXdoaXRlIGRyb3Atc2hhZG93LW1kJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNZW51T3Blbih0cnVlKX1cbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIk9wZW4gbWVudVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxNZW51IHNpemU9ezI0fSAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgIHsvKiBMb2dvICovfVxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGxnOmZsZXgtbm9uZSB0ZXh0LWNlbnRlciBsZzp0ZXh0LWxlZnRcIlxuICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjA1IH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuMyB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPXtgdGV4dC0yeGwgbGc6dGV4dC0zeGwgZm9udC1saWdodCB0cmFja2luZy13aWRlc3QgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tNTAwICR7XG4gICAgICAgICAgICAgICAgaXNTY3JvbGxlZCA/ICd0ZXh0LWd1Y2NpLWJsYWNrJyA6ICd0ZXh0LXdoaXRlIGRyb3Atc2hhZG93LWxnJ1xuICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgR1VDQ0lcbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgey8qIERlc2t0b3AgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggZmxleC0xIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtOFwiPlxuICAgICAgICAgICAgICAgIHtuYXZJdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8bW90aW9uLmxpXG4gICAgICAgICAgICAgICAgICAgIGtleT17aXRlbX1cbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEsIGR1cmF0aW9uOiAwLjYgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiI1wiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFja2luZy13aWRlIGhvdmVyOm9wYWNpdHktNzAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBpc1Njcm9sbGVkID8gJ3RleHQtZ3VjY2ktYmxhY2snIDogJ3RleHQtd2hpdGUgZHJvcC1zaGFkb3ctbWQnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbX1cbiAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgPC9tb3Rpb24ubGk+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L25hdj5cblxuICAgICAgICAgICAgey8qIEFjdGlvbiBCdXR0b25zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgaG92ZXI6b3BhY2l0eS03MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgIGlzU2Nyb2xsZWQgPyAndGV4dC1ndWNjaS1ibGFjaycgOiAndGV4dC13aGl0ZSBkcm9wLXNoYWRvdy1tZCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjEgfX1cbiAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzU2VhcmNoT3Blbih0cnVlKX1cbiAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiU2VhcmNoXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxTZWFyY2ggc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgaG92ZXI6b3BhY2l0eS03MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgIGlzU2Nyb2xsZWQgPyAndGV4dC1ndWNjaS1ibGFjaycgOiAndGV4dC13aGl0ZSBkcm9wLXNoYWRvdy1tZCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjEgfX1cbiAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJXaXNobGlzdFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8SGVhcnQgc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTIgaG92ZXI6b3BhY2l0eS03MCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgIGlzU2Nyb2xsZWQgPyAndGV4dC1ndWNjaS1ibGFjaycgOiAndGV4dC13aGl0ZSBkcm9wLXNoYWRvdy1tZCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjEgfX1cbiAgICAgICAgICAgICAgICB3aGlsZVRhcD17eyBzY2FsZTogMC45NSB9fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQ2FydE9wZW4odHJ1ZSl9XG4gICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlNob3BwaW5nIGJhZ1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8U2hvcHBpbmdCYWcgc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmJ1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbW90aW9uLmhlYWRlcj5cblxuICAgICAgey8qIE1vYmlsZSBNZW51IE92ZXJsYXkgKi99XG4gICAgICA8QW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICB7aXNNZW51T3BlbiAmJiAoXG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei01MCBsZzpoaWRkZW5cIlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWJsYWNrLzUwXCIgb25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbihmYWxzZSl9IC8+XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTAgdG9wLTAgaC1mdWxsIHctODAgYmctZ3VjY2ktd2hpdGVcIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IHg6IC0zMjAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyB4OiAwIH19XG4gICAgICAgICAgICAgIGV4aXQ9e3sgeDogLTMyMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjQsIGVhc2U6IFswLjUsIDAsIDAsIDFdIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItOFwiPlxuICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtbGlnaHQgdHJhY2tpbmctd2lkZXN0XCI+R1VDQ0k8L2gyPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yXCJcbiAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkNsb3NlIG1lbnVcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8WCBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxuYXY+XG4gICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICAgIHtuYXZJdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5saVxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpdGVtfVxuICAgICAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAtMjAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEsIGR1cmF0aW9uOiAwLjQgfX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiI1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtbGcgZm9udC1tZWRpdW0gdHJhY2tpbmctd2lkZSBob3ZlcjpvcGFjaXR5LTcwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW19XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24ubGk+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICA8L25hdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApfVxuICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG5cbiAgICAgIHsvKiBTZWFyY2ggTW9kYWwgKi99XG4gICAgICA8QW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICB7aXNTZWFyY2hPcGVuICYmIChcbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTUwXCJcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZWFzZTogWzAuNSwgMCwgMCwgMV0gfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3VjY2ktYmxhY2svOTAgYmFja2Ryb3AtYmx1ci1zbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzU2VhcmNoT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktY2VudGVyIHB0LTMyIHAtNFwiXG4gICAgICAgICAgICAgIGluaXRpYWw9e3sgeTogLTUwLCBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgeTogMCwgb3BhY2l0eTogMSB9fVxuICAgICAgICAgICAgICBleGl0PXt7IHk6IC01MCwgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGVhc2U6IFswLjUsIDAsIDAsIDFdIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIG1heC13LTR4bFwiPlxuICAgICAgICAgICAgICAgIHsvKiBDbG9zZSBCdXR0b24gKi99XG4gICAgICAgICAgICAgICAgPG1vdGlvbi5idXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzU2VhcmNoT3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtOCByaWdodC04IHAtMyB0ZXh0LXdoaXRlIGhvdmVyOm9wYWNpdHktNzAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjEgfX1cbiAgICAgICAgICAgICAgICAgIHdoaWxlVGFwPXt7IHNjYWxlOiAwLjk1IH19XG4gICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiQ2xvc2Ugc2VhcmNoXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8WCBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgICAgICA8L21vdGlvbi5idXR0b24+XG5cbiAgICAgICAgICAgICAgICB7LyogU2VhcmNoIElucHV0ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMTJcIj5cbiAgICAgICAgICAgICAgICAgIDxtb3Rpb24uaW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaFwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy10cmFuc3BhcmVudCBib3JkZXItMCBib3JkZXItYi0yIGJvcmRlci13aGl0ZS8zMCB0ZXh0LXdoaXRlIHRleHQtNHhsIGxnOnRleHQtNnhsIGZvbnQtbGlnaHQgdHJhY2tpbmctd2lkZSBwbGFjZWhvbGRlci13aGl0ZS81MCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6Ym9yZGVyLXdoaXRlIHBiLTQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgYXV0b0ZvY3VzXG4gICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuMiwgZHVyYXRpb246IDAuNiB9fVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBQb3B1bGFyIFNlYXJjaGVzICovfVxuICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi0xNlwiXG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XG4gICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuNCwgZHVyYXRpb246IDAuNiB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwIHRleHQtc20gZm9udC1tZWRpdW0gdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVzdCBtYi04XCI+XG4gICAgICAgICAgICAgICAgICAgIFBvcHVsYXIgU2VhcmNoZXNcbiAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgbWQ6Z3JpZC1jb2xzLTQgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICAgICAge1tcbiAgICAgICAgICAgICAgICAgICAgICAnSGFuZGJhZ3MnLFxuICAgICAgICAgICAgICAgICAgICAgICdTbmVha2VycycsXG4gICAgICAgICAgICAgICAgICAgICAgJ0dHIE1hcm1vbnQnLFxuICAgICAgICAgICAgICAgICAgICAgICdEaW9ueXN1cycsXG4gICAgICAgICAgICAgICAgICAgICAgJ0FjZSBTbmVha2VycycsXG4gICAgICAgICAgICAgICAgICAgICAgJ0pld2VscnknLFxuICAgICAgICAgICAgICAgICAgICAgICdXYXRjaGVzJyxcbiAgICAgICAgICAgICAgICAgICAgICAnQmVhdXR5J1xuICAgICAgICAgICAgICAgICAgICBdLm1hcCgodGVybSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt0ZXJtfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1sZWZ0IHRleHQtd2hpdGUgdGV4dC1sZyBmb250LWxpZ2h0IHRyYWNraW5nLXdpZGUgaG92ZXI6b3BhY2l0eS03MCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwIGJvcmRlci1iIGJvcmRlci13aGl0ZS8yMCBwYi0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTIwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuNSArIGluZGV4ICogMC4xLCBkdXJhdGlvbjogMC40IH19XG4gICAgICAgICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHg6IDEwIH19XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge3Rlcm19XG4gICAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBDYXRlZ29yaWVzICovfVxuICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XG4gICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuNiwgZHVyYXRpb246IDAuNiB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwIHRleHQtc20gZm9udC1tZWRpdW0gdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVzdCBtYi04XCI+XG4gICAgICAgICAgICAgICAgICAgIENhdGVnb3JpZXNcbiAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICAgICAgICAgICAge1tcbiAgICAgICAgICAgICAgICAgICAgICB7IHRpdGxlOiAnV29tZW4nLCBpdGVtczogWydSZWFkeS10by1XZWFyJywgJ0hhbmRiYWdzJywgJ1Nob2VzJywgJ0pld2VscnknXSB9LFxuICAgICAgICAgICAgICAgICAgICAgIHsgdGl0bGU6ICdNZW4nLCBpdGVtczogWydSZWFkeS10by1XZWFyJywgJ0JhZ3MnLCAnU2hvZXMnLCAnQWNjZXNzb3JpZXMnXSB9LFxuICAgICAgICAgICAgICAgICAgICAgIHsgdGl0bGU6ICdHaWZ0cycsIGl0ZW1zOiBbJ0ZvciBIZXInLCAnRm9yIEhpbScsICdCZWF1dHknLCAnSG9tZSddIH1cbiAgICAgICAgICAgICAgICAgICAgXS5tYXAoKGNhdGVnb3J5LCBjYXRlZ29yeUluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17Y2F0ZWdvcnkudGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuNyArIGNhdGVnb3J5SW5kZXggKiAwLjEsIGR1cmF0aW9uOiAwLjQgfX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXhsIGZvbnQtbGlnaHQgdHJhY2tpbmctd2lkZSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvaDU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5pdGVtcy5tYXAoKGl0ZW0sIGl0ZW1JbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxtb3Rpb24ubGlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aXRlbX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTEwIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZGVsYXk6IDAuOCArIGNhdGVnb3J5SW5kZXggKiAwLjEgKyBpdGVtSW5kZXggKiAwLjA1LCBkdXJhdGlvbjogMC4zIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIiNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzcwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwIHRleHQtc20gdHJhY2tpbmctd2lkZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmxpPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICApfVxuICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG5cbiAgICAgIHsvKiBDYXJ0L0NoZWNrb3V0IE1vZGFsICovfVxuICAgICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgICAge2lzQ2FydE9wZW4gJiYgKFxuICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHotNTBcIlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX1cbiAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBlYXNlOiBbMC41LCAwLCAwLCAxXSB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ndWNjaS1ibGFjay85MCBiYWNrZHJvcC1ibHVyLXNtXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNDYXJ0T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCB0b3AtMCBoLWZ1bGwgdy1mdWxsIG1heC13LW1kIGJnLWd1Y2NpLXdoaXRlIHNoYWRvdy0yeGxcIlxuICAgICAgICAgICAgICBpbml0aWFsPXt7IHg6ICcxMDAlJyB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IHg6IDAgfX1cbiAgICAgICAgICAgICAgZXhpdD17eyB4OiAnMTAwJScgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42LCBlYXNlOiBbMC41LCAwLCAwLCAxXSB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7LyogQ2FydCBIZWFkZXIgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNiBib3JkZXItYiBib3JkZXItZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWxpZ2h0IHRyYWNraW5nLXdpZGVcIj5TaG9wcGluZyBCYWc8L2gzPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQ2FydE9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGhvdmVyOm9wYWNpdHktNzAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiQ2xvc2UgY2FydFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFggc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBDYXJ0IENvbnRlbnQgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTZcIj5cbiAgICAgICAgICAgICAgICB7LyogU2FtcGxlIENhcnQgSXRlbXMgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICBpZDogMSxcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAnR0cgTWFybW9udCBTbWFsbCBNYXRlbGFzc8OpIFNob3VsZGVyIEJhZycsXG4gICAgICAgICAgICAgICAgICAgICAgcHJpY2U6ICckMiwxMDAnLFxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnQmxhY2snLFxuICAgICAgICAgICAgICAgICAgICAgIHNpemU6ICdTbWFsbCcsXG4gICAgICAgICAgICAgICAgICAgICAgaW1hZ2U6ICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU4NDkxNzg2NTQ0Mi1kZTg5ZGY3NmFmZDM/aXhsaWI9cmItNC4wLjMmYXV0bz1mb3JtYXQmZml0PWNyb3Amdz00MDAmcT04MCcsXG4gICAgICAgICAgICAgICAgICAgICAgcXVhbnRpdHk6IDFcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgIGlkOiAyLFxuICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICdBY2UgTGVhdGhlciBTbmVha2VyJyxcbiAgICAgICAgICAgICAgICAgICAgICBwcmljZTogJyQ2NTAnLFxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnV2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgIHNpemU6ICc5JyxcbiAgICAgICAgICAgICAgICAgICAgICBpbWFnZTogJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTQ5Mjk4OTE2LWI0MWQ1MDFkMzc3Mj9peGxpYj1yYi00LjAuMyZhdXRvPWZvcm1hdCZmaXQ9Y3JvcCZ3PTQwMCZxPTgwJyxcbiAgICAgICAgICAgICAgICAgICAgICBxdWFudGl0eTogMVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICBdLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2l0ZW0uaWR9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTQgcGItNiBib3JkZXItYiBib3JkZXItZ3JheS0xMDAgbGFzdDpib3JkZXItYi0wXCJcbiAgICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEsIGR1cmF0aW9uOiAwLjQgfX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMCBoLTIwIGJnLWdyYXktMTAwIHJvdW5kZWQgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17aXRlbS5pbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIG1iLTEgbGVhZGluZy10aWdodFwiPntpdGVtLm5hbWV9PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmNvbG9yfSDigKIgU2l6ZSB7aXRlbS5zaXplfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57aXRlbS5wcmljZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ3LTYgaC02IGJvcmRlciBib3JkZXItZ3JheS0zMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC14cyBob3ZlcjpiZy1ncmF5LTEwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e2l0ZW0ucXVhbnRpdHl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidy02IGgtNiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQteHMgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICArXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIEVtcHR5IENhcnQgU3RhdGUgKi99XG4gICAgICAgICAgICAgICAgey8qIFVuY29tbWVudCB0aGlzIHNlY3Rpb24gYW5kIGNvbW1lbnQgb3V0IHRoZSBpdGVtcyBhYm92ZSB0byBzaG93IGVtcHR5IHN0YXRlXG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTE2XCJcbiAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC42IH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFNob3BwaW5nQmFnIHNpemU9ezQ4fSBjbGFzc05hbWU9XCJteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWxpZ2h0IHRyYWNraW5nLXdpZGUgbWItMlwiPllvdXIgYmFnIGlzIGVtcHR5PC9oND5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbSBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgIERpc2NvdmVyIG91ciBsYXRlc3QgY29sbGVjdGlvbnMgYW5kIGFkZCBpdGVtcyB0byB5b3VyIGJhZ1xuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJndWNjaS1idXR0b25cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0NhcnRPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgQ29udGludWUgU2hvcHBpbmdcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICAqL31cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIENhcnQgRm9vdGVyICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ncmF5LTEwMCBwLTYgc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgey8qIFN1YnRvdGFsICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHRleHQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbGlnaHQgdHJhY2tpbmctd2lkZVwiPlN1YnRvdGFsPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj4kMiw3NTA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogU2hpcHBpbmcgSW5mbyAqL31cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIFNoaXBwaW5nIGFuZCB0YXhlcyBjYWxjdWxhdGVkIGF0IGNoZWNrb3V0XG4gICAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgICAgey8qIENoZWNrb3V0IEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgICA8bW90aW9uLmJ1dHRvblxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWd1Y2NpLWJsYWNrIHRleHQtd2hpdGUgcHktNCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYWNraW5nLXdpZGUgdXBwZXJjYXNlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBob3ZlcjpiZy1ndWNjaS1kYXJrLWdyYXlcIlxuICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wMiB9fVxuICAgICAgICAgICAgICAgICAgd2hpbGVUYXA9e3sgc2NhbGU6IDAuOTggfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBQcm9jZWVkIHRvIENoZWNrb3V0XG4gICAgICAgICAgICAgICAgPC9tb3Rpb24uYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgey8qIENvbnRpbnVlIFNob3BwaW5nICovfVxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWNlbnRlciB0ZXh0LXNtIHRyYWNraW5nLXdpZGUgaG92ZXI6b3BhY2l0eS03MCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQ2FydE9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIENvbnRpbnVlIFNob3BwaW5nXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvQW5pbWF0ZVByZXNlbmNlPlxuICAgIDwvPlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IEhlYWRlclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwiU2VhcmNoIiwiSGVhcnQiLCJTaG9wcGluZ0JhZyIsIk1lbnUiLCJYIiwiSGVhZGVyIiwiaXNNZW51T3BlbiIsInNldElzTWVudU9wZW4iLCJpc1Njcm9sbGVkIiwic2V0SXNTY3JvbGxlZCIsImlzU2VhcmNoT3BlbiIsInNldElzU2VhcmNoT3BlbiIsImlzQ2FydE9wZW4iLCJzZXRJc0NhcnRPcGVuIiwiaGFuZGxlU2Nyb2xsIiwid2luZG93Iiwic2Nyb2xsWSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwibmF2SXRlbXMiLCJoZWFkZXIiLCJjbGFzc05hbWUiLCJpbml0aWFsIiwieSIsImFuaW1hdGUiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJlYXNlIiwiZGl2IiwiYnV0dG9uIiwib25DbGljayIsImFyaWEtbGFiZWwiLCJzaXplIiwid2hpbGVIb3ZlciIsInNjYWxlIiwiaDEiLCJuYXYiLCJ1bCIsIm1hcCIsIml0ZW0iLCJpbmRleCIsImxpIiwib3BhY2l0eSIsImRlbGF5IiwiYSIsImhyZWYiLCJ3aGlsZVRhcCIsImV4aXQiLCJ4IiwiaDIiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsImF1dG9Gb2N1cyIsImg0IiwidGVybSIsInRpdGxlIiwiaXRlbXMiLCJjYXRlZ29yeSIsImNhdGVnb3J5SW5kZXgiLCJoNSIsIml0ZW1JbmRleCIsImgzIiwiaWQiLCJuYW1lIiwicHJpY2UiLCJjb2xvciIsImltYWdlIiwicXVhbnRpdHkiLCJpbWciLCJzcmMiLCJhbHQiLCJwIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Header.tsx\n"));

/***/ })

});