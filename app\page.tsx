'use client'

import { useEffect, useState } from 'react'
import Header from '@/components/Header'
import Hero from '@/components/Hero'
import FeaturedCollections from '@/components/FeaturedCollections'
import NewsletterSection from '@/components/NewsletterSection'
import Footer from '@/components/Footer'
import ProgressIndicator from '@/components/ProgressIndicator'

export default function Home() {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gucci-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl font-light tracking-widest mb-4">GUCCI</div>
          <div className="loading-shimmer h-1 w-32 mx-auto"></div>
        </div>
      </div>
    )
  }

  return (
    <main className="min-h-screen bg-gucci-white">
      <ProgressIndicator />
      <Header />
      <Hero />
      <FeaturedCollections />
      <NewsletterSection />
      <Footer />
    </main>
  )
}
